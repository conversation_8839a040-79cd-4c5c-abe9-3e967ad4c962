import { PropsCalculoVendas } from "../../../interfaces";
import CardDash from "../card";


const NewCalculoVendas: React.FC<PropsCalculoVendas> = ({ dados }) => {
    let valorTotal = 0;
    let totalVendas = 0;

    if (dados) {
        totalVendas = dados.length;
        valorTotal = dados.reduce((acc, pedido) => acc + parseFloat(pedido.total), 0);
    }

    if (!valorTotal || !totalVendas) {
        return (
            <>
            <CardDash titulo="Total de vendas">
              <div className="divCardDashboard divCardVazio">
                <ul className="ulCardVazio">
                  <li className="liCardVazio">
                    Não há dados para exibir  
                  </li>
                </ul>
              </div>
            </CardDash>
          </>
        )
    }

    return (
        <>
        <CardDash titulo="Total de vendas">
            <div className="divCardDashboard">
                <ul className="ulCardDashboard">
                    <li className="liCardDashboard">
                        Valor total: <span className="spanCardMonetario">R$ {valorTotal.toFixed(2)}</span>
                    </li>
                    <li className="liCardDashboard">
                        Quantidade total: <span className="spanCardNumero">{totalVendas}</span>
                    </li>
                </ul>
            </div>
        </CardDash>
        </>
    )
    
}

export default NewCalculoVendas