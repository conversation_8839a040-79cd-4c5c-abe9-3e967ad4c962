import {
  BrowserRouter as Router,
  Routes,
  Navigate,
  Route,
} from "react-router-dom";

import Home from "./pages/Home";


import { ProvedorAutenticacao } from "./contexts/autenticacao/AuthContext";
import RotaPrivada from "./contexts/autenticacao/RotaPrivada";
import NewLogin from "./pages/Login/newLogin";

const App = () => {
  return (
    <ProvedorAutenticacao>
      <Router>
        <Routes>
          <Route path="/" element={<Navigate to="/login" />} />
           <Route path="/login" element={<NewLogin />}> </Route>
        {/* se colocar uma pagina direfente de login, vai redirecionar para login**/}
          <Route path="/*" element={<Navigate to="/login" />}> </Route>
           <Route path="/login/*" element={<Navigate to="/login" />}> </Route>
          <Route path="/home" element={<RotaPrivada />}>
            <Route index element={<Home />} />
            <Route path="*" element={<Navigate to="/home" />} />
            <Route path="/home/<USER>" element={<Navigate to="/home" />} />
          </Route>
        </Routes>
      </Router>
     </ProvedorAutenticacao> 
  )
};

export default App;
