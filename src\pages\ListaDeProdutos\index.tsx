
import "./ListaDeProdutos.css";
import { FaBoxOpen } from "react-icons/fa6";
import { LiaTimesSolid } from "react-icons/lia";
import { RiDeleteBin6Line } from "react-icons/ri";
import { ComponenteProtegido, UsuarioRoles } from "../../contexts/autenticacao/AuthContext";

import ComponenteCarregamento from "../../contexts/carregamento/carregamento";
import useListaProdutos from "../../hooks/useListaProdutos";



const ListaDeProdutos: React.FC = () => {
  const { produtos, carregando,
    excluirProduto
  } = useListaProdutos();


  if (!produtos) {
    return <ComponenteCarregamento carregando={carregando}>
        <></>
    </ComponenteCarregamento>;
  }

  return (
    <>
     <ComponenteCarregamento carregando={carregando}>
 
     
      <div className="divProdutos cards">
        <>
        <h2>Lista de produtos</h2>
          <div className="paddingEsquerda">
            {produtos.length > 0 ? (
              <>
                {produtos.map((produto) => (
                  <div key={produto.id} className="calculoAltura itemProduto">
                    <div className="cardListaProduto">
                      <div className="cardProduto">
                        <div className="cardAltura">
                          <div className="comprimentoNomeCard">
                          <h1 className="cardNomeProduto">{produto.nome}</h1>

                          </div>
                   
                    <div className="bodyCardInformacoes">
                    <p className="texto-card-registro-vendas">ID: <strong>{produto.id}</strong></p>
                        
                          <p className="texto-card-registro-vendas">Valor: <strong>R${produto.valor}</strong></p>
                          <p className="texto-card-registro-vendas">
                            Quantidade: <strong>{produto.quantidade}</strong>
                          </p>
                          
                    </div>
                       
                          <ComponenteProtegido rolesNecessarias={[UsuarioRoles.Expedicao, UsuarioRoles.Admin]} fallback={<></>}>
                          <button
                            className="botaoExcluirProduto"
                            onClick={() => excluirProduto(produto.id)}
                          >
                            <RiDeleteBin6Line />
                          </button>
                          </ComponenteProtegido>
                        </div>
                        <div className="divIcone">
                        <FaBoxOpen className="iconeProduto"></FaBoxOpen>
                        </div>
                       
                      </div>
                    </div>
                  </div>
                ))}
              </>
            ) : (
              <>
                <div className="divSemProdutoCadastrado">
                  <div className="divIconeSemProdutoCadastrado">
                    <LiaTimesSolid className="iconeSemProdutoCadastrado" />
                    <p>Sem produtos cadastrados</p>
                  </div>
                </div>
              </>
            )}
          </div>
        </>
      </div>
      </ComponenteCarregamento>
    </>
  );
};

export default ListaDeProdutos;
