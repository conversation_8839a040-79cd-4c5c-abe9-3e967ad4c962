// ---------------------- TIPAGEM E INTERFACES TERMINAL DE VENDAS ---------------------- //

export type Produto = {
  id: number;
  nome: string;
  valor: number;
};

export type OpcaoSelecao = {
  id: number;
  valor: number;
  label: string;
};

export interface ItemPedido {
  id?: string;
  descricao: string;
  quantidade: number;
  subtotal: number;
  desconto: number;
  produtoId: number | null;
}

export interface ImagemCapturada {
  dataUri: string;
}