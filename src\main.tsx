import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'
import './global.css'
import { registerSW } from 'virtual:pwa-register'

registerSW({

  immediate: true,
  onNeedRefresh() {
    if (confirm("Uma nova versão está disponível. Deseja atualizar?")) {
      window.location.reload();
    }
  
  },
  onOfflineReady() {
  },
});


ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
