import { useEffect, useState } from "react";
import { ItemPedido, OpcaoParcelas, OpcaoSelecao, OpcaoSelect } from "../pages/TerminalVendas/Utils/TerminalTypes";
import { useAutenticacao } from "../contexts/autenticacao/AuthContext";
import { buscarProdutos } from "../pages/TerminalVendas/itensPedido/Produtos/buscarProdutos";
import { toast } from "react-toastify";
import Cookies from "js-cookie";
import api from "../apiService";
import axios from "axios";
import { pagamentoSelecionado } from "../interfaces";

import { v4 as uuidv4 } from "uuid";

const useTerminalVendas = () => {
  const [produtos, setProdutos] = useState<OpcaoSelecao[]>([]);
  const [pagamento, setPagamento] = useState("");
  const [cliente, setCliente] = useState("");
  const [itensPedido, setItensPedido] = useState<ItemPedido[]>([
    {
      descricao: null,
      quantidade: 1 || "",
      subtotal: 0.0,
      desconto: 0.0,
      produtoId: null,
      precoGenerico: null,
    },
  ]);
  const [valoresUnitarios, setValoresUnitarios] = useState<number[]>([]);

  const [descontosPorcentagem, setDescontosPorcentagem] = useState<number[]>([]);
  const [total, setTotal] = useState(0.0);
  const { dadosUsuario } = useAutenticacao();
  const [itemAberto, setItemAberto] = useState<number | null>(null);
  const [parcelas, setParcelas] = useState<number | null>(null);
  // const [pedidoId, setPedidoId] = useState<string | null>(null);
  const [arquivo, setArquivo] = useState<File | null>(null);
  const [imagemCapturada, setImagemCapturada] = useState<string | null>(null);
  const [produtoSelecionado, setProdutoSelecionado] = useState<Array<OpcaoSelect | null>>([]);
  const [parcelasSelecionadas, setParcelasSelecionadas] = useState<OpcaoParcelas | null>(null);
  const [pagamentoSelecionado, setPagamentoSelecionado] = useState<pagamentoSelecionado | null>(null);

  useEffect(() => {
    buscarProdutos(setProdutos);
  }, []);

  const limparCampos = () => {
    setPagamento("");
    setPagamentoSelecionado(null);
    setProdutoSelecionado([null]);
    setParcelasSelecionadas(null);
    setCliente("");
    setItensPedido([
      {
        descricao: null,
        quantidade: 1,
        subtotal: 0.0,
        desconto: 0.0,
        produtoId: null,
        precoGenerico: null,
      },
    ]);
    setValoresUnitarios([]);
    setDescontosPorcentagem([]);
    setTotal(0.0);
    // setPedidoId(null);
    setArquivo(null);
    setImagemCapturada(null);
  };

  useEffect(() => {
    if (produtoSelecionado) {
      const selectProduto = document.getElementById("selectProduto");
      selectProduto?.classList.remove("bordaVermelha");
    }

    if (pagamento) {
      const inputFormaPagamento = document.getElementById("selectFormaPagamento");
      inputFormaPagamento?.classList.remove("bordaVermelha");
    }
  }, [produtoSelecionado, pagamento]);

  const enviarPedido = async () => {
    const itensValidos = itensPedido.filter(item => typeof item.produtoId === "number" || item?.descricao);

    if (!itensValidos.length) {
      if (!itensValidos.length) {
        const selectProduto = document.getElementById("selectProduto");
        selectProduto?.classList.add("bordaVermelha");
      }

      toast.dismiss();
      toast.error("Por favor, adicione pelo menos um item ao pedido.");

      return;
    }

    if (!itensPedido[0].produtoId || typeof itensPedido[0].produtoId !== "number") {
      const selectProduto = document.getElementById("selectProduto");
      selectProduto?.classList.add("bordaVermelha");

      toast.dismiss();
      toast.error("Por favor, adicione pelo menos um item ao pedido.");

      return;
    }

    if (!pagamento) {
      // selecionar o input que possui o id selectFormaPagamento
      const inputFormaPagamento = document.getElementById("selectFormaPagamento");
      // aplicar borda vermelha no input
      inputFormaPagamento?.classList.add("bordaVermelha");
      toast.dismiss();
      toast.error("Por favor, selecione uma forma de pagamento.");

      return;
    }

    const hasProdutoId40 = itensValidos.some(item => item.produtoId === 40);
    const hasEmptyDescricao = itensValidos.some(item => item.produtoId === 40 && !item.descricao);

    if (hasProdutoId40 && hasEmptyDescricao) {
      toast.dismiss();
      toast.error("Por favor, para produtos genéricos, adicione uma descrição.");
      return;
    }

    if (hasProdutoId40 && itensValidos.some(item => item.produtoId === 40 && !item.precoGenerico)) {
      toast.dismiss();
      toast.error("Por favor, para produtos genéricos, adicione um valor.");
      return;
    }

    if (imagemCapturada === null || arquivo === null) {
      toast.error("Por favor, tire uma foto dos produtos.", {
        toastId: "imagemFoto",
      });
      return;
    }

    const data = {
      pagamento,
      parcelamento: parcelas || 1,
      cliente,
      itensPedido: itensValidos.map(item => ({
        quantidade: item.quantidade || 1,
        desconto: item.desconto || 0,
        produtoId: item.produtoId,
        descricao: item.descricao,
        valorProdutoGenerico: item.produtoId === 40 ? item.precoGenerico : null,
      })),
    };

    try {
      const resposta = await api.post("/pedidos", data, {
        headers: {
          "Content-type": "application/json; charset=utf-8",
          Authorization: `Bearer ${Cookies.get("token")}`,
        },
      });
      // setPedidoId(resposta.data.pedidoId.id);
      if (arquivo && resposta.data.pedidoId) {
        try {
          await enviarImagem(arquivo, resposta.data.pedidoId);
        } catch (imgError) {
          toast.error(`Erro ao enviar imagem: ${imgError}`, {
            toastId: "imagemErro",
          });
        }
      } else {
        if (!resposta.data.pedidoId) {
          toast.error("Servidor não retornou id do pedido", {
            toastId: "pedidoId",
          });
        }
      }
      toast.dismiss();
      toast.success(`Pedido efetuado com sucesso`, {
        toastId: "sucesso",
      });
      limparCampos();
    } catch (error) {
      if (axios.isAxiosError(error) && error.response) {
        if (error.response.status === 400) {
          toast.error(`${error.response.data.message}`);
        } else if (error.response.status === 500) {
          toast.error(`Falha na conexão com o servidor`, {
            toastId: "conexao",
          });
        } else {
          toast.error(`Ocorreu um erro inesperado`, {
            toastId: "erro",
          });
        }
      } else {
        toast.error(`Erro na requisição: ${error}`, {
          toastId: "requisicao",
        });
      }
    }
  };

  const enviarImagem = async (imagem: File, id: string) => {
    try {
      const formData = new FormData();
      formData.append("imagem", imagem);

      await api.post(`/pedidos/imagem/${id}`, formData, {
        headers: {
          Authorization: `Bearer ${Cookies.get("token")}`,
          "Content-Type": "multipart/form-data",
        },
      });
      setImagemCapturada(null);
    } catch (error) {
      toast.error(`Erro ao enviar imagem: ${error}`, {
        toastId: "imagemErro",
      });
    }
  };

  // ---------------------- Efeito para calcular o total do pedido sempre que houver alterações nos itens ----------------------
  useEffect(() => {
    const novoTotal = itensPedido.reduce((acumulador, item) => acumulador + item.subtotal, 0.0);
    setTotal(novoTotal);
  }, [itensPedido]);

  // ---------------------- Efeito para atualizar o valor unitário sempre que o produto for alterado ----------------------

  const manipularAlteracaoProduto = (index: number, produtoId: number) => {
    // manipularAlteracaoItem(index, "produtoId", produtoId);
    manipularAlteracaoItem(index, "produtoId", produtoId);

    // ------- Atualizar o valor unitário com base no produto selecionado -------

    atualizarValorUnitario(index, produtoId);
    const produtoSelecionado = produtos.find(p => p.id === produtoId);
    const valorUnitario = produtoSelecionado ? produtoSelecionado.valor : 0;
    const novosValoresUnitarios = [...valoresUnitarios];
    novosValoresUnitarios[index] = valorUnitario;
    setValoresUnitarios(novosValoresUnitarios);

    const valorComDesconto = valorUnitario - itensPedido[index].desconto;

    manipularAlteracaoItem(index, "subtotal", valorComDesconto);
  };

  const atualizarValorUnitario = (index: number, produtoId: number | null) => {
    if (!produtoId) {
      const novosValoresUnitarios = [...valoresUnitarios];
      novosValoresUnitarios[index] = 0;
      setValoresUnitarios(novosValoresUnitarios);
      return;
    }

    if (produtoId === 40) {
      const novosValoresUnitarios = [...valoresUnitarios];
      novosValoresUnitarios[index] = novosValoresUnitarios[index] || 0;
      setValoresUnitarios(novosValoresUnitarios);
      return;
    }

    const produtoSelecionado = produtos.find(p => p.id === produtoId);
    if (produtoSelecionado) {
      const novosValoresUnitarios = [...valoresUnitarios];
      novosValoresUnitarios[index] = produtoSelecionado.valor;
      setValoresUnitarios(novosValoresUnitarios);
    }
  };

  // ---------------------- Efeito para atualizar o valor unitário sempre que o produto for alterado ----------------------
  useEffect(() => {
    itensPedido.forEach((item, index) => {
      atualizarValorUnitario(index, item.produtoId);
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [itensPedido]);

  useEffect(() => {
    janelaItemVenda(0);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const janelaItemVenda = (index: number) => {
    // Fecha todos os itens
    fechaTodosItens();
    // Abre o item específico que foi clicado
    abreItem(index);
    setItemAberto(index);
  };

  const abreItem = (index: number) => {
    const elemento = document.querySelector(`#itemVenda${index}`);
    elemento?.classList.add("item-aberto");
    elemento?.children[0].children[1].classList.remove("add-produto-none");
    elemento?.children[0].children[2].classList.remove("add-produto-none");
    elemento?.children[1].classList.remove("add-produto-none");
    elemento?.children[2].classList.remove("add-produto-none");
  };

  // ----------------------- Função para fechar todos os itens -----------------------
  const fechaTodosItens = () => {
    itensPedido.forEach((_, index) => {
      const elemento = document.querySelector(`#itemVenda${index}`);
      elemento?.children[0].children[1].classList.add("add-produto-none");
      elemento?.children[0].children[2].classList.add("add-produto-none");
      elemento?.children[1].classList.add("add-produto-none");
      elemento?.children[2].classList.add("add-produto-none");
    });
  };

  const manipularDescontoReais = (valor: number) => {
    const valorUnitario = valoresUnitarios[itemAberto || 0] || 0;
    const valorDesconto = valor;
    const descontoReaisParaPorcentagem = (valorDesconto / valorUnitario) * 100;

    const descontoPorcentagem = descontoReaisParaPorcentagem;

    const novosDescontosPorcentagem = [...descontosPorcentagem];
    novosDescontosPorcentagem[itemAberto || 0] = descontoPorcentagem;
    setDescontosPorcentagem(novosDescontosPorcentagem);
  };

  const manipularDescontoPorcentagem = (valorPorcentagem: number) => {
    const valorUnitario = valoresUnitarios[itemAberto || 0] || 0;
    const descontoReais = arredondarParaDuasCasasDecimais((valorUnitario * valorPorcentagem) / 100);

    const novosDescontosPorcentagem = [...descontosPorcentagem];
    novosDescontosPorcentagem[itemAberto || 0] = valorPorcentagem;
    setDescontosPorcentagem(novosDescontosPorcentagem);

    manipularAlteracaoItem(itemAberto || 0, "desconto", descontoReais);
  };

  const manipularAlteracaoItem = (index: number, campo: keyof ItemPedido, valor: number | string) => {
    const novosItens = [...itensPedido];

    if (campo === "precoGenerico") {
      novosItens[index].precoGenerico = valor as number;
      novosItens[index].subtotal = calcularSubtotal(novosItens[index]?.precoGenerico, novosItens[index].quantidade);
    }

    if (novosItens[index] && Object.keys(novosItens[index]).includes(campo)) {
      (novosItens[index] as any)[campo] = valor;
    }

    if (campo === "quantidade" || campo === "desconto") {
      const valorComDesconto = (valoresUnitarios[index] || 0) - novosItens[index].desconto;
      novosItens[index].subtotal = calcularSubtotal(valorComDesconto, novosItens[index].quantidade);
    }

    setItensPedido(novosItens);
  };

  const mascaraReal = (valor: number) => {
    const valorInserido = valor;

    const valorEmReais = valorInserido.toLocaleString("pt-br", {
      style: "currency",
      currency: "BRL",
    });

    return valorEmReais;
  };

  const handleBlur = (event: React.FocusEvent<HTMLDivElement>) => {
    event.target.blur();
    if (document.activeElement instanceof HTMLElement) {
      document.activeElement.blur();
    }
  };

  const adicionarItem = () => {
    const novoItem = {
      id: uuidv4(),
      descricao: "",
      quantidade: 1,
      subtotal: 0.0,
      desconto: 0.0,
      produtoId: null,
      precoGenerico: null,
    };

    setItensPedido(prevItens => {
      const updatedItens = [...prevItens, novoItem];

      setValoresUnitarios(prevValores => [...prevValores, 0]);

      fechaTodosItens();

      setTimeout(() => {
        janelaItemVenda(updatedItens.length - 1);
      }, 0);
      return updatedItens;
    });
  };

  const removerItem = (index: number) => {
    const novosItens = [...itensPedido];
    novosItens.splice(index, 1);
    setItensPedido(novosItens);

    const novosDescontosPorcentagem = [...descontosPorcentagem];
    novosDescontosPorcentagem.splice(index, 1);
    setDescontosPorcentagem(novosDescontosPorcentagem);
  };

  const calcularSubtotal = (valorUnitarioComDesconto: number | null | undefined, quantidade: number) => {
    if (!valorUnitarioComDesconto) {
      return 0;
    }

    return arredondarParaDuasCasasDecimais(valorUnitarioComDesconto * quantidade);
  };

  const arredondarParaDuasCasasDecimais = (valor: number) => {
    return Math.round(valor * 100) / 100;
  };

  return {
    dadosUsuario,
    adicionarItem,
    arredondarParaDuasCasasDecimais,
    removerItem,
    produtos,
    setProdutos,
    calcularSubtotal,
    pagamento,
    setPagamento,
    cliente,
    setCliente,
    itensPedido,
    setItensPedido,
    valoresUnitarios,
    setValoresUnitarios,
    descontosPorcentagem,
    setDescontosPorcentagem,
    total,
    setTotal,
    itemAberto,
    setItemAberto,
    parcelas,
    setParcelas,
    arquivo,
    setArquivo,
    imagemCapturada,
    setImagemCapturada,
    produtoSelecionado,
    setProdutoSelecionado,
    parcelasSelecionadas,
    setParcelasSelecionadas,
    pagamentoSelecionado,
    setPagamentoSelecionado,
    enviarPedido,
    manipularAlteracaoProduto,
    manipularDescontoReais,
    manipularDescontoPorcentagem,
    mascaraReal,
    handleBlur,
    janelaItemVenda,
    abreItem,
    fechaTodosItens,
    manipularAlteracaoItem,
    atualizarValorUnitario,
    enviarImagem,
    limparCampos,
  };
};

export default useTerminalVendas;
