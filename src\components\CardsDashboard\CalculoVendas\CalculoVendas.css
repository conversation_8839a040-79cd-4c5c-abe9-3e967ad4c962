.custom-card {
    background-color: #f8f9fa;
    color: var(--primary-color);
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(255, 0, 0, 0.1);
    transition: transform .2s ease-in-out;
  }
  
  .custom-card:hover {
    transform: scale(1.05);
  }

  
  .card-body-custom {
    padding: 20px;
  }

  .semDadosCalculoVendas {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .semDadosCalculoVendas > p {
    color: var(--second-color);
    font-size: 20px;
    margin-top: 20px;
  }
  .maximaAltura{
    height: 100%;
  }
  .metadeAltura{
    height: 50%;
  }

  .totalVendas {
    background-color: var(--primary-color);
    border-radius: 5px;
  }

  .valorTotal {
    background-color: var(--primary-color);
    border-radius: 5px;

  }
