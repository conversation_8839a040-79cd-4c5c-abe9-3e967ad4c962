import React from 'react';
import { Form } from 'react-bootstrap';
import { useProdutoForm } from '../../hooks/useProdutoForm';

type ProdutoFormProps = {
  nomeProduto?: string;
  setNomeProduto?: React.Dispatch<React.SetStateAction<string>>;
  precoProduto?: string;
  setPrecoProduto?: React.Dispatch<React.SetStateAction<string>>;
  quantidadeProduto?: string;
  setQuantidadeProduto?: React.Dispatch<React.SetStateAction<string>>;
};

const ProdutoForm: React.FC<ProdutoFormProps> = ({
  nomeProduto: nomeExterno,
  setNomeProduto: setNomeExterno,
  precoProduto: precoExterno,
  setPrecoProduto: setPrecoExterno,
  quantidadeProduto: quantidadeExterno,
  setQuantidadeProduto: setQuantidadeExterno
}) => {
  const {
    nomeProduto: nomeInterno,
    setNomeProduto: setNomeInterno,
    precoProduto: precoInterno,
    setPrecoProduto: setPrecoInterno,
    quantidadeProduto: quantidadeInterno,
    setQuantidadeProduto: setQuantidadeInterno,
    erros,
  } = useProdutoForm();

  const nome = nomeExterno || nomeInterno;
  const setNome = setNomeExterno || setNomeInterno;

  const preco = precoExterno || precoInterno;
  const setPreco = setPrecoExterno || setPrecoInterno;

  const quantidade = quantidadeExterno || quantidadeInterno;
  const setQuant = setQuantidadeExterno || setQuantidadeInterno;

  const manipularMudancaNome = (event: React.ChangeEvent<HTMLInputElement>) => {
    setNome(event.target.value);
  };

  const manipularMudancaPreco = (event: React.ChangeEvent<HTMLInputElement>) => {
    let valor = event.target.value;
    valor = valor.replace(',', '.');
    const preco = parseFloat(valor);
    if (!isNaN(preco) || valor === '') {
      setPreco(valor);
    }
  };

  const manipularMudancaQuantidade = (event: React.ChangeEvent<HTMLInputElement>) => {
    const valor = event.target.value;
    if (!isNaN(Number(valor)) || valor === '') {
      setQuant(valor);
    }
  };

  return (
    <Form>
      <Form.Group controlId="nomeProduto">
        <Form.Label>Nome do Produto</Form.Label>
        <Form.Control 
          type="text" 
          placeholder="Insira o nome do produto" 
          value={nome} 
          onChange={manipularMudancaNome}
        />
        {erros.nome && <div style={{ color: 'red' }}>{erros.nome}</div>}
      </Form.Group>
      
      <Form.Group controlId="precoProduto">
        <Form.Label>Preço</Form.Label>
        <Form.Control 
          type="text" 
          value={preco} 
          onChange={manipularMudancaPreco}
          placeholder="Insira o preço do produto"
        />
        {erros.preco && <div style={{ color: 'red' }}>{erros.preco}</div>}
      </Form.Group>

      <Form.Group controlId="quantidadeProduto">
        <Form.Label>Quantidade</Form.Label>
        <Form.Control 
          type="number" 
          value={quantidade} 
          onChange={manipularMudancaQuantidade} 
          placeholder="Insira a quantidade"
        />
        {erros.quantidade && <div style={{ color: 'red' }}>{erros.quantidade}</div>}
      </Form.Group>
    </Form>
  );
};

export default ProdutoForm;
