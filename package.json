{"name": "frp-v2-0", "private": true, "version": "1.1.8", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview --host"}, "dependencies": {"@react-pdf/renderer": "^3.4.4", "@types/js-cookie": "^3.0.6", "@types/react-bootstrap": "^0.32.35", "@types/react-datepicker": "^6.0.1", "axios": "^1.7.4", "bootstrap": "^5.3.2", "date-fns": "^3.3.1", "dotenv": "^16.4.1", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "react": "^18.2.0", "react-bootstrap": "^2.9.2", "react-datepicker": "^6.1.0", "react-dom": "^18.2.0", "react-google-charts": "^4.0.1", "react-hook-form": "^7.49.3", "react-icons": "^5.0.1", "react-modal": "^3.16.1", "react-router-dom": "^6.22.3", "react-select": "^5.8.0", "react-toastify": "^10.0.1", "react-webcam": "^7.2.0", "uuid": "^9.0.1", "vite-plugin-pwa": "^0.21.1"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/react-modal": "^3.16.3", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^6.2.2"}}