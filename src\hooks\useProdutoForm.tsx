import { useState } from 'react';
import { ErrosProdutos } from '../interfaces';


export const useProdutoForm = () => {
  const [nomeProduto, setNomeProduto] = useState('');
  const [precoProduto, setPrecoProduto] = useState<number | string>('0');
  const [quantidadeProduto, setQuantidadeProduto] = useState<number | string>(0);
  const [erros, setErros] = useState<ErrosProdutos>({});

  const validar = (): boolean => {
    const errosTemp: ErrosProdutos = {};

    if (!nomeProduto.trim()) {
      errosTemp.nome = "Nome do produto não pode ser vazio";
    }

    const precoParsed = parseFloat(precoProduto.toString());
    if (isNaN(precoParsed) || precoParsed <= 0) {
      errosTemp.preco = "O valor precisa ser maior que zero";
    }

    const quantidadeParsed = parseInt(quantidadeProduto.toString(), 10);
    if (isNaN(quantidadeParsed) || quantidadeParsed < 0) {
      errosTemp.quantidade = "Quantidade mínima inválida";
    }

    setErros(errosTemp);
    return Object.keys(errosTemp).length === 0;
  };

  return {
    nomeProduto,
    setNomeProduto,
    precoProduto,
    setPrecoProduto,
    quantidadeProduto,
    setQuantidadeProduto,
    erros,
    validar
  };
};
