.menu-principal {
    display: flow-root;
}

.expedição-header {
    color: white;
    display: flex;
    flex-direction: column;
    margin-top: 30px;
    width: 99%;
}

.divInfosGerais {
    display: flex;
    flex-direction: row;
    align-items: start;
    justify-content: space-between;
    width: 100%;

}

.menu-principal {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.nome-remetente {
    width: 69%;
}

.inputNomeRemetente {
    width: 100%;
    height: var(--input-altura-padrao);
    background-color: var(--cor-input-desabilitado);
    border-radius: 5px;
    padding: 0 10px;
    outline: none;
    border: none;
}

.inputDataInput {
    width: 100%;
    border: none;
    outline: none;
    height: var(--input-altura-padrao);
    background-color: var(--cor-input-desabilitado);
    border-radius: 5px;
    padding: 0 10px;
}

.data {
    width: 30%;
}

.listaProdutosExpedicao {
    color: black;
}

.produtoAdicionadoExpedicao {
    display: flex;
    flex-direction: row;

    justify-content: space-between;
    margin-top: 20px;
}

.adicionarProdutoExpedicao {
    padding: 15px;
    height: auto;
}

.botaoAdicionarProdutoExpedicao {
    position: absolute;
}

.divProdutoExpedicao {
    display: flex;
    flex-direction: column;
    width: 85%;
}

.divQuantidadeExpedicao {
    display: flex;
    flex-direction: column;
    width: 15%;
}

.botaoTerminalExpedicao {
    display: flex;
    height: 40px;
    border: none;
    border-radius: 5px;
    padding: 5px;
    flex-direction: row;
    justify-content: space-between;
    margin-top: 10px;
}

.inputQuantidadeExpedicao {
    width: 95%;
  
    height: calc(var(--input-altura-padrao) - 2px);
    background-color: #fff;
    border: none;
    border-radius: 5px;
    color: black;
    padding: 0 10px;
}

.inputDataExpedicao {
    width: 100%;
    height: var(--input-altura-padrao);
    background-color: var(--cor-input-desabilitado);
    border: none;
    border-radius: 5px;
    padding: 0 10px;
}

.inputDataExpedicao::-webkit-calendar-picker-indicator {

    background: transparent;
    bottom: 0;
    color: transparent;
    cursor: pointer;
    height: auto;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    width: auto;
}

.input-desabilitado {
    color: #323332;
    background: #ffffffb5;
    border: 1px solid black;
}

.input-desabilitado:focus {
    color: #4f4d4d;
    background: #ffffffb5;
    border: 1px solid black;
    box-shadow: 0;
}

.label-desabilitada {
    background: #ffffffb5;
}

.labelProdutoExpedicao {
    margin-top: 0;
    margin-left: 8px;
}

.labelExpedicao {
    width: 100%;
}

.expedição-body {
    display: flex;
    flex-direction: column;
    width: 100%;
    margin-top: 10px;
}

.botaoRemoverItem {
    display: flex;
    height: 40px;
    border: none;
    border-radius: 5px;
    padding: 5px;
    right: 0;
    flex-direction: row;

}


@media(min-width: 450px) and (max-width: 700px) {

    .botaoTerminalExpedicao {
        width: 100px;
        display: flex;
        flex-direction: column;
        flex-wrap: wrap;
        align-content: center;
        justify-content: center;

    }
    .divProdutoExpedicao {
        width: 84%;
    }

    .divQuantidadeExpedicao {
        width: 14%;
    }

    .nome-remetente {
        width: 64%;
    }

    .data {
        width: 34%;
    }

    .pExpedicao {
        display: none;
    }
}

@media(min-width: 0px) and (max-width: 449px) {
    .divProdutoExpedicao {
        width: 79%;
    }

    .divQuantidadeExpedicao {
        width: 19%;
    }

    .nome-remetente {
        width: 62%;
    }

    .data {
        width: 36%;
    }

    .adicionarProdutoBotao p {
        display: none;
    }

    .enviarPedidoBotao p {
        display: none;
    }
}


.adicionarProdutoBotao  p{
    color: white;
}


.paddingEsquerda {
    padding: 0 8px;
}

.divInfosGerais {
    display: flex;
   
}


@media screen and (max-width: 450px) {
    .divInfosGerais {
        flex-direction: column;
    }

    .nome-remetente {
        width: 100%;
    }
    
    .data {
        width: 100%;
    }

    .botaoTerminalExpedicao {
        width: 70px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-content: center;
        overflow: hidden;
        flex-wrap: wrap;
    }
}