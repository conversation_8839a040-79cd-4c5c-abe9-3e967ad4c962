
export const manipularImagemCapturada = (dataUri: string, setCapturedImage: Function, setArquivo: Function) => {
    setCapturedImage(dataUri);
    const byteString = atob(dataUri.split(',')[1]);
    const mimeString = dataUri.split(',')[0].split(':')[1].split(';')[0];
    const ab = new ArrayBuffer(byteString.length);
    const ia = new Uint8Array(ab);
    for (let i = 0; i < byteString.length; i++) {
      ia[i] = byteString.charCodeAt(i);
    }
    const blob = new Blob([ab], { type: mimeString });
    const file = new File([blob], "capturedImage.jpg", { type: mimeString });
    setArquivo(file);  
    const element = document.querySelector('#divImagem');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }

    return {
      capturedImage: dataUri,
      arquivo: file
  };
}

export{};


