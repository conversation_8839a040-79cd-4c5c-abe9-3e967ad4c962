import React, { useEffect } from "react";
import { <PERSON><PERSON> } from "react-bootstrap";
import { CiCircle<PERSON><PERSON><PERSON>, CiCircle<PERSON><PERSON><PERSON> } from "react-icons/ci";
import { IoReload } from "react-icons/io5";
import {
	MdOutlineKeyboardArrowLeft,
	MdOutlineKeyboardArrowRight,
	MdOutlineKeyboardDoubleArrowLeft,
	MdOutlineKeyboardDoubleArrowRight,
} from "react-icons/md";
import Select from "react-select";
import ComponenteCarregamento from "../../contexts/carregamento/carregamento";
import "./aprovar.css";

import { CiCreditCardOff } from "react-icons/ci";
import { ComponenteProtegido, UsuarioRoles } from "../../contexts/autenticacao/AuthContext";
import useAprovarExpedicao from "../../hooks/useAprovarExpedicao";

const AprovarExpedição: React.FC = () => {
	const {
		expedicao,
		setStatus,
		setQuantidade,
		setFiltro,
		setPaginaAtual,
		status,
		quantidadeFiltro,
		filtro,
		paginaAtual,
		carregamento,
		alterarStatus,
		modificarPagina,
		sincronizar,
		ultimaPagina,
	} = useAprovarExpedicao();

	useEffect(() => {
		if (expedicao?.expedicoes.length && quantidadeFiltro > expedicao?.expedicoes.length) {
			setPaginaAtual(1);
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [quantidadeFiltro]);

	useEffect(() => {
		setPaginaAtual(1);

		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [filtro, quantidadeFiltro, status]);

	const renderExpedicoes = () => {
		if (expedicao?.expedicoes) {
			return expedicao?.expedicoes.map((expedicaoIndividual, index) => {
				return (
					<div key={index} className={"expedicao-div" + `${expedicaoIndividual.status === "falso" ? " cardTemporarioExpedicao" : ""}`}>
						<div className="cabecalhoExpedicao">
							<div className="numero-expedicao">
								<h2 className="titleExpedicao">
									Expedição:{" "}
									{expedicaoIndividual.id && <strong className="numero-expedicao-strong">{expedicaoIndividual.id}</strong>}
								</h2>
							</div>
							<h3 className="titleExpedicao">
								Remetente: {expedicaoIndividual.remetente && <strong>{expedicaoIndividual.remetente}</strong>}
							</h3>
						</div>
						<h3 className="titleExpedicao">
							Criado em:{" "}
							{expedicaoIndividual.criadoEm && (
								<strong>{new Date(expedicaoIndividual.criadoEm).toLocaleDateString("pt-BR", { timeZone: "UTC" })}</strong>
							)}
						</h3>
						{expedicaoIndividual.produtosEnviados.map((produto, produtoIndex) => {
							return (
								<div key={produtoIndex} className={`produto-div ${produto.status === status ? `borda${status}` : "bordaCinza"}`}>
									<h3 className="titleProduto">Produto: {produto.produto && <strong>{produto.produto}</strong>}</h3>
									<h3 className="titleProduto">Quantidade: {produto.quantidade && <strong>{produto.quantidade}</strong>}</h3>
									<h3 className="titleProduto">Status: {produto.status && <strong>{produto.status}</strong>}</h3>
									{produto.status === "Pendente" && (
										<div className="alterarStatusBotoes">
											<ComponenteProtegido rolesNecessarias={[UsuarioRoles.Admin, UsuarioRoles.Financeiro]} fallback={<></>}>
												<button
													className="botaoRecusar"
													onClick={() => alterarStatus(expedicaoIndividual.id, produto.id, "Cancelado")}
												>
													Recusar
													<CiCircleRemove className="iconeBotaoRecusar" />
												</button>
												<button
													className="botaoAprovar"
													onClick={() => alterarStatus(expedicaoIndividual.id, produto.id, "Aprovado")}
												>
													Aprovar
													<CiCircleCheck className="iconeBotaoAprovar" />
												</button>
											</ComponenteProtegido>
										</div>
									)}
								</div>
							);
						})}
					</div>
				);
			});
		} else {
			return;
		}
	};

	const nenhumaExpedicao = () => {
		return (
			<div className="nenhumaExpedicao">
				<div className="divNenhumaExpedicao">
					<CiCreditCardOff className="iconeNenhumaExpedicao" />
					<h2>Nenhuma expedição disponível.</h2>
				</div>
			</div>
		);
	};

	return (
		<div className="aprovar-container">
			<ComponenteCarregamento carregando={carregamento}>
				<div className="divHeaderExpedicoes">
					<div className="expedicoes">
						<h2>Expedições</h2>

						<div className="aprovar-header">
							<div className="filtros">
								<div className="divFiltro">
									<label className="labelAprovarExpedicao" id="statusAprovarExpedicao">
										Status:
										<div className="selectFiltro">
											<Select
												aria-labelledby="statusAprovarExpedicao"
												isSearchable={false}
												styles={{
													option: provided => ({
														...provided,
														color: "black",
														cursor: "pointer",
													}),
													control: styles => ({
														...styles,
														cursor: "pointer",
													}),
												}}
												theme={theme => ({
													...theme,
													colors: {
														...theme.colors,
														primary25: "#009f311c",
														primary: "#009f3c",
													},
												})}
												defaultValue={{ value: "Pendente", label: "Pendente" }}
												options={[
													{ value: "Pendente", label: "Pendente" },
													{ value: "Aprovado", label: "Aprovado" },
													{ value: "Cancelado", label: "Cancelado" },
												]}
												onChange={option => setStatus(option ? option.value : "Pendente")}
											/>
										</div>
									</label>
								</div>

								<div className="divFiltro">
									<label className="labelAprovarExpedicao" id="quantidadeAprovarExpedicao">
										Quantidade:
										<div className="selectFiltro">
											<Select
												aria-labelledby="quantidadeAprovarExpedicao"
												isSearchable={false}
												styles={{
													option: provided => ({
														...provided,
														color: "black",
														cursor: "pointer",
													}),
													control: styles => ({
														...styles,
														cursor: "pointer",
													}),
												}}
												theme={theme => ({
													...theme,
													colors: {
														...theme.colors,
														primary25: "#009f311c",
														primary: "#009f3c",
													},
												})}
												defaultValue={{ value: 10, label: "10" }}
												options={[
													{ value: 10, label: "10" },
													{ value: 20, label: "20" },
													{ value: 30, label: "30" },
													{ value: 50, label: "50" },
												]}
												onChange={option => setQuantidade(option ? option.value : 10)}
											/>
										</div>
									</label>
								</div>
								<div className="divFiltro">
									<label className="labelAprovarExpedicao" id="OrdenarAprovarExpedicao">
										Ordenar por:
										<div className="selectFiltro">
											<Select
												aria-labelledby="OrdenarAprovarExpedicao"
												styles={{
													option: provided => ({
														...provided,
														color: "black",
														cursor: "pointer",
													}),
													control: styles => ({
														...styles,
														cursor: "pointer",
													}),
												}}
												isSearchable={false}
												theme={theme => ({
													...theme,
													colors: {
														...theme.colors,
														primary25: "#009f311c",
														primary: "#009f3c",
													},
												})}
												defaultValue={{ value: "DESC", label: "Mais recentes" }}
												options={[
													{ value: "ASC", label: "Mais antigos" },
													{ value: "DESC", label: "Mais recentes" },
												]}
												onChange={option => {
													if (typeof option === "object" && option !== null && "value" in option) {
														setFiltro(option.value);
													} else {
														setFiltro("ASC");
													}
												}}
											/>
										</div>
									</label>
								</div>

								<div className="divBotaoSincronizar">
									<Button className="botaoAtualizar" onClick={sincronizar}>
										<IoReload className="iconPDF iconAtualizar" />
									</Button>
								</div>
							</div>
						</div>
					</div>

					<div id="rowExpedicoes">{expedicao?.expedicoes.length === 0 ? nenhumaExpedicao() : renderExpedicoes()}</div>
				</div>
				<div className="expedicoes">
					<div className="paginacao">
						<button className="botaoPaginas" onClick={() => modificarPagina(1)} disabled={paginaAtual === 1}>
							<MdOutlineKeyboardDoubleArrowLeft className="iconPagina" />
						</button>
						<button className="botaoPaginas" onClick={() => modificarPagina(paginaAtual - 1)} disabled={paginaAtual === 1}>
							<MdOutlineKeyboardArrowLeft className="iconPagina" />
						</button>
						<div className="paginaDiv">
							Página {paginaAtual} de {ultimaPagina}
						</div>
						<button className="botaoPaginas" onClick={() => modificarPagina(paginaAtual + 1)} disabled={paginaAtual === ultimaPagina}>
							<MdOutlineKeyboardArrowRight className="iconPagina" />
						</button>
						<button className="botaoPaginas" onClick={() => modificarPagina(ultimaPagina)} disabled={paginaAtual === ultimaPagina}>
							<MdOutlineKeyboardDoubleArrowRight className="iconPagina" />
						</button>
					</div>
				</div>
			</ComponenteCarregamento>
		</div>
	);
};

export default AprovarExpedição;
