
.cameraContainer {
    position: fixed;
    width: 100%;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: black;
    z-index: 9999;
}

.cameraButtonContainer {
    position: absolute;
    bottom: 5%;
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
}

.imagemButton {
   background-color: var(--status-imagem-color);
   border: none;
   height: 40px;
   width: 200px;
   border-radius: 5px;

}   

.imagemButton:hover {
    background-color: var(--background-status-imagem-color);
}

@media screen and (max-width: 768px) {
    .imagemButton {
        width: 30%;
        left: 35%;
        right: 35%;
    }

    
}


.imageIcon {
    width: 30px;
    height: 30px;
    margin-top: 5px;
}


.cameraButton {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toggleCameraButton {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: rgba(0, 0, 0, 0.5); 
    color: white; 
    border: none; 
    border-radius: 50%; 
    padding: 10px; 
    transition: background-color 0.3s; 
}

.toggleCameraButton:hover {
    background-color: rgba(0, 0, 0, 0.7); 
}

.cameraCloseButton {
    position: absolute;
    top: 10px;
    left: 10px;
    cursor: pointer;
    z-index: 10; 
    background-color: rgba(0,0,0,0.5); 
    border-radius: 50%; 
    padding: 5px;
}

