
import "../ListaDeProdutos/ListaDeProdutos.css";
import { LiaTimesSolid } from "react-icons/lia";
import ComponenteCarregamento from "../../contexts/carregamento/carregamento";
import useListaUsuarios from "../../hooks/useListaUsuarios";
import { IoPerson } from "react-icons/io5";



const ListaDeUsuarios: React.FC = () => {
  const {  usuarios, carregando, formatarNomes } = useListaUsuarios();



  if (!usuarios) {
    return <ComponenteCarregamento carregando={carregando}>
        <></>
    </ComponenteCarregamento>;
  }

  return (
    <>
     <ComponenteCarregamento carregando={carregando}>
      <h2>Lista de Usuários</h2>
     
      <div className="divProdutos cards">
        <>
          <div className="paddingEsquerda">
            {usuarios.length > 0 ? (
              <>
                {usuarios.map((usuario) => (
                  <div key={usuario.id} className="calculoAltura itemProduto">
                    <div className="cardListaProduto">
                      <div className="mb-4 cardProduto">
                        <div className="cardAltura">
                          <h1 className="cardNomeProduto">{formatarNomes(usuario.nome)}</h1>
                          <p className="texto-card-registro-vendas">ID: <strong>{usuario.id}</strong></p>
                          <p>{formatarNomes(usuario.role) || "Não informado"}</p>
                    
                
                        </div>
                        {/* <FaBoxOpen className="iconeProduto"></FaBoxOpen> */}
                        <IoPerson className="iconeProduto"></IoPerson>
                      </div>
                    </div>
                  </div>
                ))}
              </>
            ) : (
              <>
                <div className="divSemProdutoCadastrado">
                  <div className="divIconeSemProdutoCadastrado">
                    <LiaTimesSolid className="iconeSemProdutoCadastrado" />
                    <p>Sem usuários cadastrados</p>
                  </div>
                </div>
              </>
            )}
          </div>
        </>
      </div>
      </ComponenteCarregamento>
    </>
  );
};

export default ListaDeUsuarios;
