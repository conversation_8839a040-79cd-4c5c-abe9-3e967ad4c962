import { View , Text, Image, StyleSheet} from "@react-pdf/renderer"
import Imagem<PERSON>ogo from "../../../../assets/images/Logo/FRP_PRETO.png";

export const CabecalhoPdf = ({ id }: { id: number }) => {
    return (
        <>
            <View style={CabecalhoEstilo.cabecalho}>
                <View style={CabecalhoEstilo.campoLogo}>
                    <Image cache src={ImagemLogo} style={CabecalhoEstilo.imagemLogo} />
                </View>

                <View style={CabecalhoEstilo.campoTitulo}>
                    <Text style={CabecalhoEstilo.titulo}>Comprovante de Pagamento</Text>
                </View>

                <View style={CabecalhoEstilo.campoNrProposta}>
                    <View style={CabecalhoEstilo.caixaNrProposta}>
                        <Text style={{ fontSize: 9 }}>Proposta:</Text>
                        <View style={CabecalhoEstilo.campoId}>
                            <Text style={CabecalhoEstilo.idTexto}>{id}</Text>
                        </View>
                    </View>
                </View>
            </View>
        </>
    )
}


const CabecalhoEstilo = StyleSheet.create({
    cabecalho: {
        width: "100%",
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        flexDirection: "row",
        height: 70,
        borderBottom: "1px solid black",
    },
    campoLogo: {
        width: "27.5%",
        display: "flex",
        height: 70,
        justifyContent: "center",
        alignItems: "center",
    },
    imagemLogo: {
        maxWidth: 110, maxHeight: 60, width: "auto", height: "auto" 
    },
    campoTitulo: {
        width: "45%",
        display: "flex",
        height: 70,
        justifyContent: "center",
        alignItems: "center",
    },
    titulo: {
        fontSize: 18, fontWeight: "bold"
    },
    campoNrProposta: {
        width: "27.5%",
        display: "flex",
        height: 70,
        justifyContent: "center",
        alignItems: "center",
    },
    caixaNrProposta: {
        width: 130,
        display: "flex",
        justifyContent: "flex-start",
        alignItems: "flex-start",
        border: "1px solid black",
        height: 60,
        padding: 5
    },
    campoId: {
        width: "100%", display: "flex", justifyContent: "center", alignItems: "center", marginTop: 5 
    },
    idTexto: {
        fontSize: 18, fontWeight: "ultrabold"
    }
})