import { ReactDatePickerProps } from "react-datepicker";

export interface CalendarDateInputProps extends Omit<ReactDatePickerProps, "onChange"> {
	onClick?: () => void;
	value?: string;
}

export interface datasInterface {
	dataInicial: Date | undefined;
	setDataInicial: (dataInicial: Date | null) => void;
	dataFinal: Date | undefined;
	setDataFinal: (dataFinal: Date | null) => void;
	placeholder?: string;
}
