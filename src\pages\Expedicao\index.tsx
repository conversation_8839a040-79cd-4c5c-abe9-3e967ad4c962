import Select from "react-select";
import "react-toastify/dist/ReactToastify.css";
import "./Expedicao.css";
import { AiOutlinePlus } from "react-icons/ai";
import { RiSendPlaneFill } from "react-icons/ri";

import useTerminalExpedicao from "../../hooks/useTerminalExpedicao";

const Expedicao = () => {
	const {
		dadosUsuario,
		produtoItem,
		produtoSelecionado,
		produtoEnviado,
		setProdutoSelecionado,
		setProduto,
		adicionarProduto,
		removerProduto,
		enviarExpedicao,
	} = useTerminalExpedicao();


	return (
		<div className="criarExpedicao paddingEsquerda">
			<div className="expedição-header">
				<div className="menu-principal">
					<div className="divInfosGerais">
						<div className="nome-remetente">
							<div className="labelNomeRemetente">
								<label className="">
									Nome do remetente:
									<input
										className="inputNomeRemetente"
										placeholder="Nome do remetente"
										value={
											dadosUsuario?.nomeUsuario ||
											"Nome do Usuário Não Disponível"
										}
										readOnly
									/>
								</label>
							</div>
						</div>
						<div className="data">
							<div>
								<label className="form-label labelExpedicao">
									Data:
									{/* <input
										className="form-control inputDataExpedicao input-desabilitado"
										value={new Date().toLocaleDateString()}
										readOnly
									/> */}
									<input
										className="inputDataInput"
										value={new Date().toLocaleDateString()}
										readOnly
									/>
								</label>
							</div>
						</div>
					</div>
				</div>
				<div className="expedição-body">
					<div>
						{produtoEnviado.map((item, index) => (
							<div
								key={index}
								className="add-produto adicionarProdutoExpedicao"
							>
								<div className="produtoAdicionadoExpedicao">
									<div className="divProdutoExpedicao">
										<label className="form-label labelProdutoExpedicao">
											Produto:
											<Select
												id="produtoExpedicao"
												styles={{
													option: (provided) => ({
														...provided,
														color: "black",
														cursor: 'pointer',
													}),
													control: (styles) => ({
														...styles,
														cursor: 'pointer',
													}),

												}}
												isSearchable={false}
												className="listaProdutosExpedicao"
												theme={(theme) => ({
													...theme,
													colors: {
														...theme.colors,
														primary25: "#009f311c",
														primary: "#009f3c",
													},
												})}
												placeholder="Selecione um produto"
												options={Object.values(
													produtoItem
												).map((p) => ({
													id: p.id,
													label: p.label,
													valor: p.valor,
													value: p.id,
												}))}
												value={
													produtoItem.find(
														(p) =>
															p.id ===
															produtoSelecionado[
															index
															]
													) || null
												}
												onChange={(selectedOption) => {
													const novosProdutosSelecionados: (
														| number
														| null
													)[] = [
															...produtoSelecionado,
														];
													if (selectedOption) {
														novosProdutosSelecionados[
															index
														] = selectedOption.id;
													} else {
														novosProdutosSelecionados[
															index
														] = null;
													}
													setProdutoSelecionado(
														novosProdutosSelecionados
													);
												}}
											/>
										</label>
									</div>
									<div className="divQuantidadeExpedicao">
										<label className="form-label labelProdutoExpedicao">
											<p>Quant.</p>
											<input
												type="number"
												id="quantidadeExpedicao"
												className="form-control inputQuantidadeExpedicao eventoFocus"
												placeholder="Qnt"
												value={item.quantidade}
												onChange={(e) => {
													const newProdutos = [
														...produtoEnviado,
													];
													newProdutos[
														index
													].quantidade = parseInt(
														e.target.value
													);
													setProduto(newProdutos);
												}}
											/>
										</label>
									</div>
								</div>
								{produtoEnviado.length > 1 && (
									<button
										className="botaoRemoverItem"
										onClick={() => removerProduto(index)}
									>
										<svg
											stroke="currentColor"
											fill="currentColor"
											strokeWidth="0"
											viewBox="0 0 1024 1024"
											height="1em"
											width="1em"
											xmlns="http://www.w3.org/2000/svg"
										>
											<path d="M512 0C229.232 0 0 229.232 0 512c0 282.784 229.232 512 512 512 282.784 0 512-229.216 512-512C1024 229.232 794.784 0 512 0zm0 961.008c-247.024 0-448-201.984-448-449.01 0-247.024 200.976-448 448-448s448 200.977 448 448-200.976 449.01-448 449.01zm181.008-630.016c-12.496-12.496-32.752-12.496-45.248 0L512 466.752l-135.76-135.76c-12.496-12.496-32.752-12.496-45.264 0-12.496 12.496-12.496 32.752 0 45.248L466.736 512l-135.76 135.76c-12.496 12.48-12.496 32.769 0 45.249 12.496 12.496 32.752 12.496 45.264 0L512 557.249l135.76 135.76c12.496 12.496 32.752 12.496 45.248 0 12.496-12.48 12.496-32.769 0-45.249L557.248 512l135.76-135.76c12.512-12.512 12.512-32.768 0-45.248z"></path>
										</svg>
									</button>
								)}
							</div>
						))}
						<div className="footer">
							<button
								className="adicionarProdutoBotao botaoTerminalExpedicao"
								onClick={adicionarProduto}
							>
								<AiOutlinePlus className="adicionarProdutoIcone" />
								<p className="pExpedicao"> Adicionar produto </p>
							</button>
						</div>
						<button
							className="enviarPedidoBotao botaoTerminalExpedicao"
							onClick={enviarExpedicao}
						>
							<p className="pExpedicao"> Enviar expedição </p>
							<RiSendPlaneFill className="enviarPedidoIcone" />
						</button>
					</div>
				</div>
			</div>
		</div>
	);
};
export default Expedicao;
