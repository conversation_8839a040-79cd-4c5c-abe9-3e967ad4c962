import React from "react";
import { Chart } from "react-google-charts";

import RelatorioGeral from "../../components/Relatorios/relatorioGeral";

import ReactPDF from "@react-pdf/renderer";
import { ptBR } from "date-fns/locale";
import { registerLocale } from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { AiOutlineLoading3Quarters } from "react-icons/ai";
import { MdDownload, MdError } from "react-icons/md";
import ReactModal from "react-modal";
import Select from "react-select";
registerLocale("pt-BR", ptBR);

ReactModal.setAppElement("#root");

import AvisoNaoSuportaPDF, { verificarSeNavegadorSuportaPDF } from "../../components/AvisoPDF";
import ModalFRP from "../../components/Modal";
import useDashboard from "../../hooks/useDashboard";

import NewCalculoVendas from "../../components/CardsDashboard/CalculoVendas/newCalculoVendas";
import FormaPagamentosMaisUsadas from "../../components/CardsDashboard/FormasPagamentos";
import NewProdutosMaisVendidos from "../../components/CardsDashboard/ProdutosMaisVendidos/newProdutos";
import NewVendaVendedor from "../../components/CardsDashboard/VendaVendedor/newVendasVendedor";
import CalendarInput from "../../components/dateComponente";
import ComponenteCarregamento from "../../contexts/carregamento/carregamento";
import "./Dashboard.css";
import CardEntradaSaida from "../../components/Entrada/saida";

const Dashboard: React.FC = () => {
    const {
        dataFinal,
        setDataFinal,
        dataInicial,
        setDataInicial,
        dadosPedidos,
        dadosGraficoLinha,
        datasValidas,
        mostrarPorValor,
        setMostrarPorValor,
        mostrarModalRelaorio,
        setMostrarModalRelatorio,
        dadosRelatorio,
        paginaAtualGrafico,
        setPaginaAtualGrafico,
        alturaMaximaGrafico,
        setFormaPagamento,
        setIdVendedor,
        vendedor,
        dadosPorPagina,
        botaoAdicionarPorPagina,
        dataInicialFormatada,
        dataFinalFormatada,
        manipularAbrirPDF,
        dadosGraficoPaginados,
        graficoPorPagina,
        vendedorSelecionado,
        setVendedorSelecionado,
        formaPagamentoSelecionada,
        setFormaPagamentoSelecionada,
        carregando,
        entradaPortas,
        saidaPortas,
    } = useDashboard();

    registerLocale("pt-BR", ptBR);

    return (
        <>
            <div className="dashboard-container">
                <React.Suspense fallback={<div>Carregando...</div>}>
                    <div className="mt-12 align-items-center datasDashboard">
                        <div className="divFiltroDashboard">
                            <div className="formFiltroDashboard">
                                <div className="rowDataDashboard">
                                    <div className="filtros-divs ">
                                        <label htmlFor="" className="labelDashboard">
                                            Data
                                        </label>

                                        <CalendarInput
                                            dataFinal={dataFinal || undefined}
                                            dataInicial={dataInicial || undefined}
                                            setDataFinal={setDataFinal}
                                            setDataInicial={setDataInicial}
                                        />
                                    </div>

                                    <div className="filtros-divs">
                                        <label htmlFor="selectDashboard" className="labelDashboard">
                                            Vendedor
                                        </label>
                                        <Select
                                            className="selectDashboard-filtro"
                                            id="selectDashboard"
                                            styles={{
                                                option: provided => ({
                                                    ...provided,
                                                    color: "black",
                                                }),
                                            }}
                                            // defaultValue={{ value: 0, label: "Todos" }}
                                            value={vendedorSelecionado}
                                            isSearchable={false}
                                            placeholder="Vendedor"
                                            theme={theme => ({
                                                ...theme,
                                                colors: {
                                                    ...theme.colors,
                                                    primary25: "#009f311c",
                                                    primary: "#009f3c",
                                                },
                                            })}
                                            options={[
                                                { value: 0, label: "Todos" },
                                                ...Object.values(vendedor).map(vendedor => ({
                                                    value: vendedor.id,
                                                    label: vendedor.nome,
                                                })),
                                            ]}
                                            onChange={e => {
                                                if (e) {
                                                    setIdVendedor(e.value);
                                                    setVendedorSelecionado(e);
                                                }
                                            }}
                                        />
                                    </div>

                                    <div className="filtros-divs">
                                        <label htmlFor="selectDashboardPagamento" className="labelDashboard">
                                            Forma de Pagamento
                                        </label>
                                        <Select
                                            className="selectDashboard-filtros-pagamento"
                                            isSearchable={false}
                                            id="selectDashboardPagamento"
                                            styles={{
                                                option: provided => ({
                                                    ...provided,
                                                    color: "black",
                                                }),
                                            }}
                                            value={formaPagamentoSelecionada}
                                            theme={theme => ({
                                                ...theme,
                                                colors: {
                                                    ...theme.colors,
                                                    primary25: "#009f311c",
                                                    primary: "#009f3c",
                                                },
                                            })}
                                            placeholder="Forma de pagamento"
                                            options={[
                                                { value: "", label: "Todas" },
                                                { value: "Dinheiro", label: "Dinheiro" },
                                                { value: "Débito", label: "Débito" },
                                                { value: "Crédito", label: "Crédito" },
                                                { value: "PIX", label: "PIX" },
                                                {
                                                    value: "Desconto em folha",
                                                    label: "Desconto em folha",
                                                },
                                                {
                                                    value: "Livre de débito",
                                                    label: "Livre de débito",
                                                },
                                            ]}
                                            onChange={e => {
                                                if (e) {
                                                    setFormaPagamentoSelecionada(e);
                                                    setFormaPagamento(e.value);
                                                }
                                            }}
                                        />
                                    </div>

                                    <div className="filtros-divs">
                                        <label htmlFor="" className="labelDashboard">
                                            {"‎ ‎"}
                                        </label>
                                        <button
                                            disabled={dadosPedidos?.length === 0 || !datasValidas || !dadosPedidos}
                                            className="btnGerarRelatorio"
                                            onClick={e => {
                                                e.preventDefault();
                                                manipularAbrirPDF();
                                            }}
                                        >
                                            Gerar relatório
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </React.Suspense>

                <ComponenteCarregamento carregando={carregando}>
                    <div>
                        <div className="mt-4 flex-equal-height">
                            <div className="cardDashboad">
                                <NewProdutosMaisVendidos dados={dadosPedidos} />
                            </div>

                            <div className="cardDashboad">
                                <NewCalculoVendas dados={dadosPedidos} />
                            </div>

                            <div className="cardDashboad">
                                <NewVendaVendedor dados={dadosPedidos} />
                            </div>

                            <div className="cardDashboad">
                                <FormaPagamentosMaisUsadas dados={dadosPedidos} />
                            </div>
                        </div>

                        <div style={{ padding: "5px" }}>
                            <CardEntradaSaida entradas={entradaPortas} saidas={saidaPortas} />
                        </div>
                        <div className="mt-4">
                            {dadosGraficoLinha.length < 1 ? (
                                <></>
                            ) : (
                                <>
                                    {" "}
                                    <div className="mostrarPor">
                                        <h5 className="labelMostrarPor">Mostrar por:</h5>
                                        <button
                                            className="botaoGrafico"
                                            onClick={() => setMostrarPorValor(!mostrarPorValor)}
                                        >
                                            {mostrarPorValor ? "Quantidade de vendas" : "Valor total"}
                                        </button>
                                    </div>
                                </>
                            )}
                        </div>
                        <div className="mt-4">
                            <div className="borda-verde">
                                <div className="cardGrafico">
                                    <div className="card-header-custom card-title-dash">
                                        <h5>Progressão de vendas</h5>
                                    </div>
                                    {dadosGraficoLinha.length > 0 || !dadosPedidos ? (
                                        <div className="card-body-custom">
                                            <div className="divQuantidadePorPagina">
                                                <div className="maisPorPagina">
                                                    {/* Se tiver somente 1 venda, esconder botão mais por página */}
                                                    {botaoAdicionarPorPagina || dadosGraficoLinha.length > 4 ? (
                                                        <>
                                                            {!botaoAdicionarPorPagina ? (
                                                                <button
                                                                    className="botaoMaisPorPagina"
                                                                    onClick={graficoPorPagina}
                                                                >
                                                                    <span className="textoAdicionarPorPagina">
                                                                        Ver mais
                                                                    </span>
                                                                </button>
                                                            ) : (
                                                                <button
                                                                    className="botaoMaisPorPagina"
                                                                    onClick={graficoPorPagina}
                                                                >
                                                                    <span className="textoAdicionarPorPagina">
                                                                        Ver menos
                                                                    </span>
                                                                </button>
                                                            )}
                                                        </>
                                                    ) : (
                                                        <></>
                                                    )}
                                                </div>
                                            </div>
                                            <Chart
                                                width={"100%"}
                                                height={"300px"}
                                                chartType="ColumnChart"
                                                loader={<div>Carregando gráfico</div>}
                                                data={[
                                                    [
                                                        "Dia",
                                                        mostrarPorValor ? "Valor" : "Vendas",
                                                        { role: "tooltip", type: "string" },
                                                    ],
                                                    ...dadosGraficoPaginados().map(dataEntry => [
                                                        dataEntry.x,
                                                        dataEntry.y,
                                                        `${dataEntry.nomeDia}, ${dataEntry.x}\n${dataEntry.label}\n `,
                                                    ]),
                                                ]}
                                                options={{
                                                    backgroundColor: "black",
                                                    hAxis: {
                                                        title: "Dia",
                                                        textStyle: { color: "white" },
                                                        titleTextStyle: { color: "white" },
                                                        gridlines: { color: "gray" },
                                                    },
                                                    vAxis: {
                                                        maxValue: alturaMaximaGrafico,
                                                        minValue: 0,
                                                        title: mostrarPorValor ? "Valor Total do dia" : "Vendas",
                                                        textStyle: { color: "white" },
                                                        titleTextStyle: { color: "white" },
                                                        gridlines: { color: "gray" },
                                                        format: "0",
                                                    },
                                                    legend: "none",
                                                    colors: ["#20bf5c"],
                                                    tooltip: { isHtml: true },
                                                    chartArea: {
                                                        backgroundColor: "black",
                                                        width: "80%",
                                                        height: "70%",
                                                    },
                                                    bar: { groupWidth: "50%" },
                                                    animation: {
                                                        duration: 300,
                                                        easing: "out",
                                                        startup: true,
                                                    },
                                                    annotations: {
                                                        textStyle: {
                                                            color: "white",
                                                            fontSize: 14,
                                                        },
                                                    },
                                                }}
                                                rootProps={{ "data-testid": "1" }}
                                            />
                                            <div className="divPaginacaoGrafico">
                                                <div>
                                                    <button
                                                        className="botaoGraficoDashboard"
                                                        disabled={paginaAtualGrafico === 0}
                                                        onClick={() => setPaginaAtualGrafico(paginaAtualGrafico - 1)}
                                                    >
                                                        {"<"}
                                                    </button>
                                                </div>
                                                <div className="paginaAtualGrafico">
                                                    <p>
                                                        {paginaAtualGrafico + 1} de{" "}
                                                        {Math.ceil(dadosGraficoLinha.length / dadosPorPagina)}
                                                    </p>
                                                </div>
                                                <div>
                                                    <button
                                                        className="botaoGraficoDashboard"
                                                        disabled={
                                                            paginaAtualGrafico >=
                                                            Math.ceil(dadosGraficoLinha.length / dadosPorPagina) - 1
                                                        }
                                                        onClick={() => setPaginaAtualGrafico(paginaAtualGrafico + 1)}
                                                    >
                                                        {">"}
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="divNaoHaDadosGrafico">
                                            <p className="textoNaoHaDados">Não há dados para exibir</p>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                </ComponenteCarregamento>

                <ModalFRP estaAberto={mostrarModalRelaorio} aoSolicitarFechar={() => setMostrarModalRelatorio(false)}>
                    <div className="GeralRelatorio">
                        <div className="divVisualizacaoPDF">
                            {verificarSeNavegadorSuportaPDF() ? (
                                <ReactPDF.PDFViewer className="pdfViewer">
                                    <RelatorioGeral
                                        dados={dadosRelatorio}
                                        dataInicial={dataInicialFormatada}
                                        dataFinal={dataFinalFormatada}
                                    />
                                </ReactPDF.PDFViewer>
                            ) : (
                                <AvisoNaoSuportaPDF />
                            )}
                        </div>

                        <div className="divBaixarPDF">
                            <button className="baixarRelatorio">
                                <ReactPDF.PDFDownloadLink
                                    document={
                                        <RelatorioGeral
                                            dados={dadosRelatorio}
                                            dataInicial={dataInicialFormatada}
                                            dataFinal={dataFinalFormatada}
                                        />
                                    }
                                    fileName="comprovante.pdf"
                                    className="pdfLink"
                                >
                                    {({ blob, loading, error }) =>
                                        loading ? (
                                            <AiOutlineLoading3Quarters className="iconeBaixarRelatorioPedidos" />
                                        ) : error ? (
                                            <MdError className="iconeBaixarRelatorioPedidos" />
                                        ) : blob ? (
                                            <MdDownload className="iconeBaixarRelatorioPedidos" />
                                        ) : (
                                            <p>Download</p>
                                        )
                                    }
                                </ReactPDF.PDFDownloadLink>
                            </button>
                        </div>
                    </div>
                </ModalFRP>
            </div>
        </>
    );
};

export default Dashboard;
