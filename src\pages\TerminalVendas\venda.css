* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: var(--font-main);
}

.itemVenda {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 10px;
  padding-top: 30px;
  margin: 5px;
  transition: all 0.2s linear;

}


.add-produto {
  background: var(--background-primary-color);
  border-radius: 17px;
  padding-top: 25px;
  margin-top: 15px;
  position: relative;
  padding-bottom: 20px;
  margin-bottom: 20px;
  transition: all 0.2s linear;
  display: flex;
  flex-direction: column;
  max-height: 1000px;
  border: 1px solid var(--primary-color);
  max-width: 100%;
  transition: all 0.2s linear;
}

.rowTerminalVendas {
  display: flex;
  flex-wrap: wrap;
}

.labelHeader2 {
  margin-top: -5px;
  margin-bottom: 0px;
  color: var(--second-color);
}


.divMainItemVenda {

  width: 100%;
}

.inputDescontos {
  width: 25%;
  padding-left: 2px;
  outline: none;
}


.inputDescontosReaisInput {
  width: 99%;
  height: calc(var(--input-altura-padrao) - 2px);
  background-color: #fff;
  border: none;
  color: #000000;
  border-radius: 5px;
  outline: none;
  padding-left: 10px;
}

.divFooterItemVendas {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  width: 100%;
}



.inputDescontoPorcentagem {
  width: 22%;
  outline: none;
}

.inputDescontoPorcentagemInput {
  width: 99%;
  height: calc(var(--input-altura-padrao) - 2px);
  background-color: #fff;
  border: none;
  color: #000000;
  border-radius: 5px;
  outline: none;
  padding-left: 10px;
}

.inputValorUnitarioDesconto {
  width: 25%;
  outline: none;
}

.valorUnitarioDescontoInput {
  width: 99%;
  height: calc(var(--input-altura-padrao) - 2px);
  background-color: #fff;
  border: none;
  color: #000000;
  border-radius: 5px;
  outline: none;
  padding-left: 10px;
}

.inputSubtotalItemVenda {
  width: 25%;
  outline: none;
}

.inputSubtotalItemVendaInput {
  width: 99%;
  height: calc(var(--input-altura-padrao) - 2px);
  background-color: #fff;
  border: none;
  color: #000000;
  border-radius: 5px;
  padding-left: 10px;
  outline: none;
}


.headerTerminalVendas {
  display: flex;
  flex-wrap: wrap;
}

.headerTerminalVendas2 {
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;
  width: 100%;

}

.inputVendedor {
  width: 25%;
  outline: none;
}

.inputTotal {
  margin-top: 5px;
  width: 48%;
  left: 0;
  outline: none;
}

.inputCliente {
  right: 0;
  margin-top: 5px;
  width: 51%;

  outline: none;

}



.inputTotalInput {
  width: 100%;
  height: calc(var(--input-altura-padrao) - 2px);
  background-color: var(--cor-input-desabilitado);
  color: #000000;
  border: none;
  border-radius: 5px;
  padding-left: 10px;
  outline: none;

}

.inputClienteInput {
  width: 100%;
  height: calc(var(--input-altura-padrao) - 2px);
  right: 0;
  color: #000000;
  border: none;
  outline: none;
  border-radius: 5px;
  padding-left: 10px;

}


.inputVendedorInput {
  width: 100%;
  height: calc(var(--input-altura-padrao) - 2px);
  background-color: var(--cor-input-desabilitado);
  border: none;
  border-radius: 5px;
  padding-left: 10px;
  outline: none;
}


.inputDataTerminal {
  width: 25%;
  outline: none;
  margin-left: 5px;
}

.inputDataInput {
  width: 100%;
  outline: none;
  height: calc(var(--input-altura-padrao) - 2px);
  background-color: var(--cor-input-desabilitado);
  border: none;
  border-radius: 5px;
  padding-left: 10px;

}

.inputFormaPagamento {
  width: 48%;
  margin-left: 5px;
  outline: none;
}

.inputFormaPagamentoParcelas {
  width: 22%;
  margin-left: -10px;
}

.divInputParcelas {
  width: 24%;
}


.inputValorUnitario {
  width: 30%;
  padding-left: 15px;
  padding-right: 15px;
}

@media screen and (max-width: 1250px) {
  .inputVendedor {
    width: 49%;
  }

  .inputTotal {
    margin-top: 0px;
    right: 0;
    width: 49%;
  }

  .inputCliente {
    margin-top: 0px;
    right: 0;
    width: 50%;
  }

  .inputFormaPagamento {
    width: 49%;
    margin-left: 0px;
  }

  .inputFormaPagamentoParcelas {
    width: 49%;
    margin-left: 0px;
  }

  .divInputParcelas {
    width: 49%;
  }

  .inputDataTerminal {
    width: 49%;
    margin-left: 0px;

  }

}


@media screen and (max-width: 875px) {
  .inputVendedor {
    width: 100%;
  }

  .inputDataTerminal {
    width: 100%;
    margin-left: 0px;
  }

  .inputFormaPagamento {
    width: 100%;
    margin-left: 0px;
  }

  .inputFormaPagamentoParcelas {
    width: 100%;
    margin-left: 0px;
  }

  .divInputParcelas {
    width: 100%;
  }

  .inputVendedor {
    width: 100%;
  }

  .inputTotal {
    width: 100%;
    padding-right: 0px;
  }

  .inputTotalInput {
    width: 100%;
    margin-right: 0;
  }
  
  .inputClienteInput {
    width: 100%;
    margin-left: 0;
  }

  .labelCliente {
    margin-left: 0;
  }

  .inputCliente {
    padding-left: 0px;
    width: 100%;
  }

  input {
    width: 100%;
    margin-bottom: 10px;
  }

  .selectFormaPagamento {
    width: 100%;
    margin-bottom: 10px;
  }

  .divFooterItemVendas {
        width: 100%;
        display: flex;
        flex-direction: column;
        
  }
  
  .inputDescontos {
        width: 100%;
  }
  
  .inputDescontoPorcentagem {
        width: 100%;
  }
  
      .inputValorUnitarioDesconto {
        width: 100%;
   
      }
  
      .inputSubtotalItemVenda {
        width: 100%;
      }

      .inputValorUnitario {
        width: 100%;
        padding-right: 0;
      }
      
      .quantidadeInput {
        width: 100%;
        margin: 0 auto;
      }
  
      .divInputUnitario {
        margin-top: 10px;
      }
      
      .inputDescricao {
        margin: 0;
      }
      
      .inputDescontosReaisInput {
        width: 100%;
      }
      
      .inputDescontoPorcentagemInput {
        width: 100%;
      }
      
      .valorUnitarioDescontoInput {
        width: 100%;
      }

      .inputSubtotalItemVendaInput {
        width: 100%;
      }
  
}

.form-group {
  margin-bottom: 1rem;
}

.divHeaderPedido {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  width: 100%;
  background-color: red;
}


.imagemButton {
  width: 25%;
  height: auto;
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  align-content: center;
  align-items: center;
  position: fixed;
  bottom: 20px;
  z-index: 1;
  box-shadow: 0px -1px 5px var(--background-primary-color);
}

.input-group:focus-within {
  color: var(--bs-body-color);
  background-color: var(--bs-body-bg);
  border-color: #86b7fe;
  outline: 0;
  box-shadow: 0 0 0 0.12rem #009f3cfa;
  border-radius: 5px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.input-group-desabilitado:focus-within {
  color: #323332;
  background-color: black !important;
  box-shadow: none;
  border: none;
  outline: none;
}

.input-group-desabilitado .form-control:focus {
  border: 1px solid black !important;
}

.input-group .form-control:focus {
  background-color: none;
  box-shadow: none;
  border: 1px solid white;
}

.label-desabilitada {
  border-color: black;
}

.input-desabilitado:focus {
  box-shadow: none;
  outline: 0;
}

.add-produto {
  background: var(--background-primary-color);
  border-radius: 17px;
  padding-top: 25px;
  margin-top: 15px;
  position: relative;
  padding-bottom: 20px;
  margin-bottom: 20px;
  transition: all 0.2s linear;
  display: flex;
  flex-direction: column;
  max-height: 1000px;
  border: 1px solid var(--primary-color);
  max-width: 100%;
  transition: all 0.2s linear;
}

.add-produto .item-aberto {
  background: rgb(39, 39, 39);
  border: 1px solid red;
}

.add-produto-minimizado {
  opacity: 1;
  transition: all 0.2s linear;
}

.add-produto-none {
  display: none;
}



.divHeaderItemVenda {
  width: 100%;
  justify-content: space-between;
  display: flex;
}


.selectProduto {
  width: 100%;
}

.valorUnitarioAtivo {
  width: 100%;
  height: calc(var(--input-altura-padrao) - 2px);
  background-color: #fff;
  border: none;
  color: #000000;
  border-radius: 5px;
  outline: none;
  padding-left: 10px;
  padding-top:5px;
}


.valorUnitarioInput {
  width: 100%;
  height: calc(var(--input-altura-padrao) - 2px);
  background-color: var(--cor-input-desabilitado);
  border: none;
  border-radius: 5px;
  padding-left: 10px;
}

.quantidadeInput {
  width: 20%;
}


.quantidadeInputInput {
  width: 100%;
  height: calc(var(--input-altura-padrao) - 2px);
  background-color: #fff;
  border: none;
  color: #000000;
  border-radius: 5px;
  outline: none;
  padding-left: 10px;
  padding-top:5px;
}

@media screen and (max-width: 875px) {
  .divHeaderItemVenda {
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  .quantidadeInput {
    width: 100%;
  }

  .inputTotal {
    width: 100%;
  }

  .selectProduto {
    width: 100%;
  }

  .inputValorUnitario {
    width: 100%;
    padding-left: 0px;
  }

  .quantidadeInput {
    padding-left: 0px;
  }

}

/* .divMainItemVenda {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
} */

.inputDescricao {
  width: 100%;
  margin: 15px 0;
}

.inputDescricaoInput {
  width: 100%;
  height: calc(var(--input-altura-padrao) * 1.5);
  border: none;
  border-radius: 5px;
  padding-left: 10px;
  resize: none;
  outline: none;
  padding: 5px;
}




.custom-success-toast {
  background-color: var(--primary-color-up-variant);
  color: var(--second-color);
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 0.5s, transform 0.5s;
}

.custom-success-toast.show {
  opacity: 1;
  transform: translateY(0);
}

.form-label {
  color: var(--second-color);
}

.botaoRemoverItem {
  position: absolute;
  top: -3px;
  right: -9px;
  border: none;
  background-color: transparent;
}

.botaoRemoverItem:hover {
  background-color: transparent !important;
}

.botaoRemoverItem:active {
  background-color: transparent !important;
}

.botaoRemoverItem:hover svg {
  color: var(--status-cancelado-color) !important;
  transition: all 0.3s;
}

.botoesPedido {
  display: flex;
  width: 100%;
  margin-right: 10px;
  flex-direction: row;
  justify-content: center;
}

.labelProduto {
  margin-top: 0px;
}

.botaoRemoverItem sgv path {
  color: #ff00005e;
  width: 100%;
}

.botaoRemoverItem svg {
  width: 30px;
  height: 30px;
}


.adicionarProdutoIcone {
  height: 20px;
  width: 20px;
}

.adicionarProdutoBotao {
  display: flex;
  align-items: center;
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  position: fixed;
  bottom: 20px;
  left: 270px;
  box-shadow: 5px -1px 5px var(--background-primary-color);
}

.adicionarProdutoBotao p {
  margin-top: 0px;
  margin-bottom: 0px;
  padding-left: 5px;
}

.adicionarProdutoBotao:hover {
  background-color: var(--primary-color-down-variant);
  border-color: var(--primary-color-down-variant);
}

.adicionarProdutoBotao:active {
  background-color: var(--primary-color-down-variant) !important;
  border-color: var(--primary-color-down-variant) !important;
}

.enviarPedidoIcone {
  /* height: 18px;
  width: 18px; */
  margin-right: 5px;
}

.enviarPedidoBotao {
  display: flex;
  align-items: center;

  justify-content: center;
  color: white;
  background-color: var(--primary-color);
 border: none;
 padding: 5px;
  position: fixed;
  border-radius: 5px;

  bottom: 20px;
  right: 20px;
  box-shadow: -5px -1px 5px var(--background-primary-color);
}



.enviarPedidoBotao:hover {
  background-color: var(--primary-color-down-variant);
  border-color: var(--primary-color-down-variant);
}

.enviarPedidoBotao:active {
  background-color: var(--primary-color-down-variant) !important;
  border-color: var(--primary-color-down-variant) !important;
}

.mensagemErro {
  color: red;
  font-weight: bold;
  background-color: #ffcccc;
  padding: 10px;
  border-radius: 5px;
}



.adicionarProdutoIcone {
  height: 20px;
  width: 20px;
  margin-top: -2px;
}

.adicionarProdutoBotao {
  display: flex;
  align-items: center;
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  position: fixed;
  bottom: 20px;
  left: 270px;
  box-shadow: 5px -1px 5px var(--background-primary-color);
}


.adicionarProdutoBotao p {
  margin-top: 0px;
  margin-bottom: 0px;
}

.adicionarProdutoBotao:hover {
  background-color: var(--primary-color-down-variant);
  border-color: var(--primary-color-down-variant);
}

.adicionarProdutoBotao:active {
  background-color: var(--primary-color-down-variant) !important;
  border-color: var(--primary-color-down-variant) !important;
}

.enviarPedidoIcone {
  height: 18px;
  width: 18px;
  margin-top: -2px;
}



.enviarPedidoBotao p {
  margin-top: 0px;
  margin-bottom: 0px;
  padding-right: 5px;
}

.enviarPedidoBotao:hover {
  background-color: var(--primary-color-down-variant);
  border-color: var(--primary-color-down-variant);
}

.enviarPedidoBotao:active {
  background-color: var(--primary-color-down-variant) !important;
  border-color: var(--primary-color-down-variant) !important;
}

.mensagemErro {
  color: red;
  font-weight: bold;
  background-color: #ffcccc;
  padding: 10px;
  border-radius: 5px;
}




@media screen and (max-width: 480px) {
  .enviarPedidoBotao p {
    display: none;
  }

  .enviarPedidoBotao {
    display: flex;
    width: 80px;
    align-items: center;
    justify-content: center;
    height: 40px;
  }

  .adicionarProdutoBotao p {
    display: none;
  }

  .adicionarProdutoBotao {
    display: flex;
    width: 80px;
    height: 40px;
    align-items: center;
    justify-content: center;
  }

  .imagemButton {
    height: auto;
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    align-content: center;
    align-items: center;
    position: fixed;
    bottom: 20px;
    z-index: 1;
    box-shadow: 0px -1px 5px var(--background-primary-color);
    left: calc(50% - 40px);
    width: 80px;
  }

 
}

@media screen and (max-width: 875px) {
  
  

  .adicionarProdutoBotao {
    left: 10px;
  }

  .enviarPedidoBotao {
    right: 10px;
  }
}

.imageIcon {
  width: 25px;
  height: 25px;
}

.divImagem .divBordasImagem img {
  width: 340px;
  height: 406px;
  margin: 5px;
  object-fit: cover;
  padding: 20px;
  margin: 20px;
}

.divImagem .divBordasImagem {
  width: 100%;
  display: flex;
  justify-content: center;
  border: 2px solid green;
  background-color: rgba(61, 61, 61, 0.188);
  border-radius: 10px;
}

.divImagem {
  margin: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

@media screen and (max-width: 575px) {
  .divImagem {
    width: 100%;
   
  }



  
  .inputDescricao {
    width: 100%;
    margin: 0;
    outline: none;
  }


  .divImagem .divBordasImagem {
    width: 100%;
    display: flex;
    justify-content: center;
    border: 2px solid green;
    background-color: rgba(61, 61, 61, 0.188);
    border-radius: 10px;
  } 


}

.divMainImagemProduto {
  display: flex;
  justify-content: center;
}

.footer {
  position: relative;
  bottom: 0;
  height: 100px;
}

.eventoFocus:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.07rem #009f3cfa;
}

.selectFiltro {
  color: black;
}

.labelTerminalVendas {
  width: 100%;
  color: var(--second-color);
}

.selectTerminalVendas {
  color: black;
  width: 100%;
  height: 100%;
  /* height: auto; */

}

.formTerminalVendas {
  padding: 0 15px;
  width: 100%;
  height: 100%;
}

.labelCemPorCento {
  width: 100%;
}

.selectTerminalVendas::placeholder {
  font-size: 1em;
}

.terminalVenda {
  max-width: 100%;
  margin: none;
  padding-left: 2px;
  margin-right: auto;
  margin-left: auto;

}



.textNoWrap {
  text-wrap: nowrap;
}

.bordaVermelha {
  border: 3px solid rgba(255, 0, 0, 0.452);
  border-radius: 5px;
  transition: all 0.2s linear;
  animation: piscar 1s infinite;
}

.bordaVermelha::after {
  content: "Campo obrigatório";
  color: red;
  font-size: 0.8em;
  position: absolute;
  bottom: -8px;
  right: 0;
  padding: 5px;
  background-color: rgba(255, 255, 255, 0);
  border-radius: 5px;
}

@keyframes piscar {
  0% {
    border: 3px solid rgba(255, 0, 0, 0.452);
  }

  50% {
    border: 3px solid rgba(255, 0, 0, 0.452);
  }

  100% {
    border: 3px solid rgba(255, 0, 0, 0);
  }
}

.botoesTerminalVendas {
  width: 130px;
  margin-right: 8px;
  display: flex;
  justify-content: center;
  align-items: center; 
  color: white;
  height: 40px;
  border: none;
  border-radius: 5px;
}


@media screen and (max-width: 575px) {

  .botoesTerminalVendas {
    width: 100px;
    margin-right: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 4px;
    color: white;
    height: 40px;
    border-radius: 5px;
    border: none;
    flex-direction: row;
    flex-wrap: nowrap;
    align-content: center;
}
}

.CabecalhoTerminalVendas {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 7px;
}

.divItensPedido {
  width: 100%;
  
}



@media screen and (max-width: 820px) {

  #root {
    --alturaMobile: 60px;
    --tamanhoLabel: 20px;
  }

  .expedição-header {
    width: 100%
  }

  .inputVendedor {
    margin: 5px 0;
    
  }

  .inputFormaPagamento {
    margin: 5px 0;
  }
  
  #formTerminalVendas .inputDataTerminal {
    margin: 5px 0;
  }

  .inputTotal {
    margin: 5px 0;
  }

  .inputCliente {
    margin: 5px 0;
  }

  .inputVendedorInput {
    height: var(--alturaMobile);
    font-size: var(--tamanhoLabel);
  }

  .labelCemPorCento {
    font-size: var(--tamanhoLabel);
  }

  #formTerminalVendas .css-1ff06q0-control {
    height: var(--alturaMobile);
  }

  .labelTerminalVendas {
    font-size: var(--tamanhoLabel);
  }
  
  #formTerminalVendas .inputDataInput {
    height: var(--alturaMobile) !important;
    font-size: var(--tamanhoLabel);
  }


  .inputTotalInput {
    height: var(--alturaMobile);
    font-size: var(--tamanhoLabel);
  }

  .labelHeader2 {
    font-size: var(--tamanhoLabel);
  }

  .inputClienteInput {
    height: var(--alturaMobile);
    font-size: var(--tamanhoLabel);
  }

  .valorUnitarioInput {
    height: var(--alturaMobile);
    font-size: var(--tamanhoLabel);
  }

  .quantidadeInputInput {
    height: var(--alturaMobile);
  }

  .inputDescricaoInput {
    font-size: var(--tamanhoLabel);
  }

  .inputDescontosReaisInput {
    height: var(--alturaMobile);
    font-size: var(--tamanhoLabel);
  }

  .inputDescontoPorcentagemInput {
    height: var(--alturaMobile);
    font-size: var(--tamanhoLabel);
  }

  .valorUnitarioDescontoInput {
    height: var(--alturaMobile);
    font-size: var(--tamanhoLabel);
  }
  
  .inputSubtotalItemVendaInput {
    height: var(--alturaMobile);
    font-size: var(--tamanhoLabel);
  }


  #formTerminalVendas .css-1ff06q0-control {
    height: var(--alturaMobile);
    min-height: var(--alturaMobile);
  }

  #formTerminalVendas .css-1ff06q0-control:hover {
    height: var(--alturaMobile);
  }


 #formTerminalVendas .css-25w86a-control {
    height: var(--alturaMobile);
    min-height: var(--alturaMobile);
  }
  
 
  .botoesTerminalVendas {
    width: 150px;
    font-size: 24px;
    height: 70px;
    border-radius: 5px;
  }

  .imagemButton {
    height: 70px !important;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  #formTerminalVendas .adicionarProdutoIcone {
    height: 40px;
    width: 40px;

  }
  
  #formTerminalVendas .enviarPedidoIcone {
    height: 40px;
    width: 40px;

  }
  
  #formTerminalVendas .imageIcon {
    width: 40px;
    height: 40px;
  }
}