.componente {
    position: relative;
    width: 100%;
    height: 90vh;
}

.container-carregamento {
    animation: fadeIn 2s ease;
    margin: 0 auto;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.418);
}

.porta-completa {
    display: flex;
    justify-content: center;
    width: 500px;
    flex-direction: column;
    flex-wrap: wrap;
    align-content: space-around;
    align-items: center;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

.batente {
    width: 120px;
    height: 210px;
    border-left: 15px solid #d7d7d7;
    border-right: 15px solid #d7d7d7;
    border-top: 15px solid #d7d7d7;
    box-shadow: rgb(9 61 20 / 19%) 0px 54px 55px, rgb(0 0 0 / 19%) 0px -12px 30px, inset rgba(1, 219, 48, 0.185) 0px -20px 6px, rgba(1, 219, 48, 0.185) 0px 12px 13px, rgb(208 213 209 / 19%) 0px -3px 5px;
    perspective: 1000px;
}

.porta {
    width: 90px;
    height: 195px;
    background-color: white;
    position: relative;
    transform-origin: left;
    animation: swing 4s infinite ease-in-out;
    box-shadow: 5px 0 10px rgba(255, 255, 255, 0.5);
}

.fechadura {
    width: 10px;
    height: 20px;
    background-color: #606060;
    position: absolute;
    right: 10px;
    top: 90px;
    border-radius: 2px;
    box-shadow: inset 0 0 2px #000000, 0 0 2px #FFFFFF;
}

.fechadura:before {
    content: '';
    position: absolute;
    top: 5px;
    left: 0px;
    width: 10px;
    height: 10px;
    background-color: #C0C0C0;
    border-radius: 50%;
    box-shadow: 0 0 4px #000000;
}

.carregamento {
    color: rgba(255, 255, 255, 0.507);

    text-align: center;
    animation: fadeInOut 2s infinite;
    margin-top: 20px;
}

.carregamento h1 {
    font-size: 35px;
    font-weight: 700;
    text-shadow: 0 0 10px rgba(30, 223, 13, 0.288);
}


@keyframes fadeInOut {

    0%,
    100% {
        opacity: 0;
    }

    50% {
        opacity: 1;
    }
}

@keyframes swing {
    0% {
        transform: rotateY(0deg);
    }

    50% {
        transform: rotateY(90deg);
    }

    100% {
        transform: rotateY(0deg);
    }
}