import React, { createContext, useContext, useEffect, useState } from "react";
import Cookies from "js-cookie";
import { jwtDecode } from "jwt-decode";
import api from "../../apiService";

interface DadosUsuario {
  id: number;
  nomeUsuario: string;
  roles: string[];
}

interface TokenDecodificado {
  exp: number;
  id: number;
  username: string;
  roles: string;
}

export enum UsuarioRoles {
  Vendedor = "VENDEDOR",
  Financeiro = "FINANCEIRO",
  Expedicao = "EXPEDICAO",
  Admin = "ADMINISTRADOR",
  Gestor = "GESTOR",
}

export interface TipoContextoAutenticacao {
  token: string | null;
  refreshToken: string | null;
  dadosUsuario: DadosUsuario | null;
  entrar: (
    token: string,
    tempoExpiracaoTokenEmHoras: number,
    refreshToken?: string,
    tempoExpiracaoRefreshToken?: number
  ) => void;
  sair: () => void;
  atualizarToken: () => Promise<void>;
}

export const ContextoAutenticacao = createContext<TipoContextoAutenticacao | undefined>(undefined);

const useAutenticacaoEstadoInicial = () => {
  const [token, definirToken] = useState<string | null>(Cookies.get("token") || null);
  const [refreshToken, definirRefreshToken] = useState<string | null>(Cookies.get("refreshToken") || null);
  const [dadosUsuario, definirDadosUsuario] = useState<DadosUsuario | null>(null);

  return { token, refreshToken, dadosUsuario, definirToken, definirRefreshToken, definirDadosUsuario };
};

export const decodificarTokenRefresh = (token: string) => {
  const tokenDecodificado = jwtDecode<TokenDecodificado>(token);

  const tempoDeExpiracao = tokenDecodificado.exp;
  const horas = (tempoDeExpiracao - Date.now() / 1000) / 3600;

  return Math.round(horas * 100) / 100;
};

export const ProvedorAutenticacao: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { token, refreshToken, dadosUsuario, definirToken, definirRefreshToken, definirDadosUsuario } =
    useAutenticacaoEstadoInicial();

  useEffect(() => {
    const verificarExpiracao = async () => {
      const tokenAtual = token ? jwtDecode<TokenDecodificado>(token) : null;
      const refreshTokenAtual = refreshToken ? jwtDecode<TokenDecodificado>(refreshToken) : null;

      if (tokenAtual && Date.now() >= tokenAtual.exp * 1000) {
        await atualizarToken();
      } else if (refreshTokenAtual && Date.now() >= refreshTokenAtual.exp * 1000) {
        sair();
      }
    };
    verificarExpiracao();

    const intervalId = setInterval(verificarExpiracao, 50 * 60 * 1000);
    return () => clearInterval(intervalId);
  }, [token, refreshToken]);

  useEffect(() => {
    if (token) {
      const decodificado = jwtDecode<TokenDecodificado>(token);
      definirDadosUsuario({
        id: decodificado.id,
        nomeUsuario: decodificado.username,
        roles: decodificado.roles.split(","),
      });
    } else {
      definirDadosUsuario(null);
    }
  }, [token]);

  const entrar = (
    novoToken: string,
    tempoExpiracaoTokenEmHoras: number,
    novoRefreshToken?: string,
    tempoExpiracaoRefreshToken?: number
  ) => {
    definirToken(novoToken);

    Cookies.set("token", novoToken, {
      expires: new Date(Date.now() + tempoExpiracaoTokenEmHoras * 3600000),
      secure: true,
      sameSite: "strict",
    });

    if (novoRefreshToken && tempoExpiracaoRefreshToken) {
      definirRefreshToken(novoRefreshToken);
      Cookies.set("refreshToken", novoRefreshToken, {
        expires: new Date(Date.now() + tempoExpiracaoRefreshToken * 3600000),
        secure: true,
        sameSite: "strict",
      });
    }
  };

  const sair = () => {
    definirToken(null);
    definirRefreshToken(null);
    Cookies.remove("token");
    Cookies.remove("refreshToken");
    localStorage.removeItem("componenteAtivo");
  };

  const atualizarToken = async () => {
    try {
      const resposta = await api.post("/auth/refresh", { refresh_token: refreshToken });

      const horasExpToken = decodificarTokenRefresh(resposta.data.access_token);
      const horasExpRefreshToken = decodificarTokenRefresh(resposta.data.refresh_token);

      entrar(resposta.data.access_token, horasExpToken, resposta.data.refresh_token, horasExpRefreshToken);
    } catch (erro) {
      console.error("Erro ao atualizar o token: ", erro);

      sair();
    }
  };

  return (
    <ContextoAutenticacao.Provider value={{ token, refreshToken, entrar, sair, atualizarToken, dadosUsuario }}>
      {children}
    </ContextoAutenticacao.Provider>
  );
};

export const temPermissao = (dadosUsuario: DadosUsuario | null, rolesNecessarias: UsuarioRoles[]) => {
  if (!dadosUsuario) return false;
  return rolesNecessarias.some(role => dadosUsuario.roles.includes(role));
};

export const ComponenteProtegido: React.FC<{
  rolesNecessarias: UsuarioRoles[];
  fallback: React.ReactNode;
  children: React.ReactNode;
}> = ({ rolesNecessarias, fallback, children }) => {
  const { dadosUsuario } = useAutenticacao();
  return temPermissao(dadosUsuario, rolesNecessarias) ? <>{children}</> : <>{fallback}</>;
};

export const useAutenticacao = () => {
  const contexto = useContext(ContextoAutenticacao);

  if (!contexto)
    throw new Error(
      "useAutenticacao deve ser usado dentro de um ProvedorAutenticacao, verifique se o componente está dentro de um <ProvedorAutenticacao />"
    );
  return contexto;
};
