import { Document, Page, StyleSheet, View } from "@react-pdf/renderer";
import { useEffect, useState } from "react";
import { DadosRelatorioVendas } from "../../../interfaces";
import api from "../../../apiService";
import <PERSON><PERSON> from "js-cookie";
import { BaseRelatorio } from "./componentesPdf/relatorioBase";

function GeradorDePDF(props: { id: number }) {
  const [dados, setDados] = useState<DadosRelatorioVendas | null>(null);

  const buscarDados = async () => {
    try {
      const token = Cookie.get("token");
      const resposta = await api.get(`/pedidos/dados/${props.id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      setDados(resposta.data);
    } catch (erro) {
      console.error("Erro ao buscar dados:", erro);
    }
  };

  useEffect(() => {
    buscarDados();
  }, [props.id]);

  if (!dados) return <div>Carregando...</div>;

  const itensPorPagina = 9;
  const paginasDeItens = [];

  for (let i = 0; i < dados.itensPedido.length; i += itensPorPagina) {
    paginasDeItens.push(dados.itensPedido.slice(i, i + itensPorPagina));
  }

  return (
    <>
      <Document
        title={`Relatório de Venda - ${props.id}`}
        author="FRP - Feirão Pormade"
        creator="FRP - Feirão Pormade"
        producer="FRP - Feirão Pormade"
        subject="Relatorio de Venda"
        keywords="Relatorio de Venda, Feirão Pormade"
      >
        {paginasDeItens.map((itens, index) => (
          <Page id={`page_${props.id}, ${index}`} orientation="portrait" size="A4">
            <View style={EstiloPDF.pagina}>
              <View style={EstiloPDF.metadeSuperior} fixed>
                <BaseRelatorio dados={dados} itens={itens} index={index} />
              </View>

              <View style={EstiloPDF.metadeInferior} fixed>
                <BaseRelatorio dados={dados} itens={itens} index={index} />
              </View>
            </View>
          </Page>
        ))}
      </Document>
    </>
  );
}

const EstiloPDF = StyleSheet.create({
  pagina: {
    width: "100%",
    height: "100%",
    backgroundColor: "#000",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    flexDirection: "column",
  },
  metadeSuperior: { width: "100%", height: "50%", backgroundColor: "#fff" },
  metadeInferior: { width: "100%", height: "50%", backgroundColor: "#fff", borderTop: "1px dashed #565656" },
});

export default GeradorDePDF;
