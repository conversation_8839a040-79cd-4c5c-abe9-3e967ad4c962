
import React, { useEffect, useRef, useState } from 'react';
import AprovarExpedição from '../pages/AprovarExpedição';
import { useAutenticacao } from '../contexts/autenticacao/AuthContext';
import Dashboard from '../pages/Dashboard';
import Venda from '../pages/TerminalVendas';
import NovoRegistro from '../pages/RegistroVendas/novoregistro';
import ListaDeProdutos from '../pages/ListaDeProdutos';
import Expedicao from '../pages/Expedicao';
import ListaDeUsuarios from '../pages/ListaUsuarios/newLista';

// const Submit = (e: React.FormEvent<HTMLFormElement>): boolean => {
//   e.preventDefault(); 
//   try {
//     e.currentTarget.submit();
//     return true
//   } catch (error) {
//     return false
//   }
// };

const useHome = () => {
  const { dadosUsuario } = useAutenticacao();
  const { sair } = useAutenticacao();
  const [componenteAtivo, setComponenteAtivo] =
    useState<React.ReactNode | null>(null);
  const [abrirMenu, setAbrirMenu] = useState(false);
  const menuLateral = useRef<HTMLDivElement | null>(null);
  const [alternarIcone, setAlternarIcone] = useState(false);
  const [estiloInformacoes, setEstiloInformacoes] =
    useState("secaoInformacoes");
  const [linkAtivo, setLinkAtivo] = useState<string | null>(null);
  const [abirPopupAdicionarProdutos, setAbrirPopupAdicionarProdutos] =
    useState(false);
  const [abrirSubmenuUsuarios, setAbrirSubmenuUsuarios] = useState(false);

  const manipularCliques = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    setAbrirMenu(false);
  };


  useEffect(() => {
    const nomeComponente = localStorage.getItem("componenteAtivo");

    switch (nomeComponente) {
      case "Dashboard":
        setComponenteAtivo(<Dashboard />);
        setLinkAtivo("dashboard");
        break;
      case "Venda":
        setComponenteAtivo(<Venda />);
        setLinkAtivo("terminal");
        break;
      case "NovoRegistro":
        setComponenteAtivo(<NovoRegistro />);
        setLinkAtivo("registro");
        break;
      case "AprovarExpedicao":
        setComponenteAtivo(<AprovarExpedição />);
        setLinkAtivo("aprovarExpedicao");
        break;
      case "ListaDeProdutos":
        setComponenteAtivo(<ListaDeProdutos />);
        setLinkAtivo("lista");
        break;
      case "ListaDeUsuarios":
        setComponenteAtivo(<ListaDeUsuarios />);
        setLinkAtivo("usuarios");
        break;
      case "Expedicao":
        setComponenteAtivo(<Expedicao />);
        setLinkAtivo("expedicao");
        break;
    }
  }, []);


  useEffect(() => {
    if (dadosUsuario?.roles.includes("EXPEDICAO") && !componenteAtivo) {

      setComponenteAtivo(<Expedicao />);
      setLinkAtivo("expedicao");

    } else if (
      !dadosUsuario?.roles.includes("EXPEDICAO") &&
      !componenteAtivo &&
      dadosUsuario?.roles !== undefined
    ) {
      setComponenteAtivo(<Dashboard />);
      setLinkAtivo("dashboard");
    }

  }, [dadosUsuario]);





  const manipularClickMenuDashboard = (
    e: React.MouseEvent<HTMLAnchorElement>
  ) => {
    manipularCliques(e);
    setComponenteAtivo(<Dashboard />);
    setLinkAtivo("dashboard");
    localStorage.setItem("componenteAtivo", "Dashboard");
  };

  const manipularClickMenuTerminalVendas = (
    e: React.MouseEvent<HTMLAnchorElement>
  ) => {
    manipularCliques(e);
    setComponenteAtivo(<Venda />);
    setLinkAtivo("terminal");
    localStorage.setItem("componenteAtivo", "Venda");
  };

  const manipularClickRegistroVendas = (
    e: React.MouseEvent<HTMLAnchorElement>
  ) => {
    manipularCliques(e);
    setComponenteAtivo(<NovoRegistro />);
    setLinkAtivo("registro");
    localStorage.setItem("componenteAtivo", "NovoRegistro");
  };

  const manipularClickMenuProdutos = (
    e: React.MouseEvent<HTMLAnchorElement>
  ) => {
    manipularCliques(e);
    setComponenteAtivo(<ListaDeProdutos />);
    setLinkAtivo("lista");
    localStorage.setItem("componenteAtivo", "ListaDeProdutos");
  };


  // useEffect(() => {
  //   setLinkAtivo("dashboard");
  // }, []);


  const manipularClickExpedição = (e: React.MouseEvent<HTMLAnchorElement>) => {
    manipularCliques(e);
    setComponenteAtivo(<Expedicao />);
    setLinkAtivo("expedicao");
    localStorage.setItem("componenteAtivo", "Expedicao");
  };

  const manipularClickAprovarExpedição = (
    e: React.MouseEvent<HTMLAnchorElement>
  ) => {
    manipularCliques(e);
    setComponenteAtivo(<AprovarExpedição />);
    setLinkAtivo("aprovarExpedicao");
    localStorage.setItem("componenteAtivo", "AprovarExpedicao");
  };

  const manipularClickPopupAdicionarProduto = (
    e: React.MouseEvent<HTMLAnchorElement>
  ) => {
    e.preventDefault();
    setAbrirPopupAdicionarProdutos(true);

  };

  const manipularClickFecharPopup = () => {
    setAbrirPopupAdicionarProdutos(false);
    setAbrirMenu(false);
  };


  const manipularClickMenuUsuarios = (e: React.MouseEvent<HTMLAnchorElement>) => {
    manipularCliques(e)
    setComponenteAtivo(<ListaDeUsuarios />);
    setLinkAtivo("usuarios");
    localStorage.setItem("componenteAtivo", "ListaDeUsuarios");
  }
  const alterarIconeFuncao = () => {
    setAlternarIcone(!alternarIcone);
  };


  const mostrarSecaoInformacoes = () => {
    setEstiloInformacoes("mostraSecaoInformacoes");
  }


  const aoClicarForaSecaoInformacoes = (e: MouseEvent) => {
    if (
      menuLateral.current &&
      !menuLateral.current.contains(e.target as Node) &&
      !(e.target as Element).closest(".botaoAbrirSubMenuUsuario") &&
      !(e.target as Element).closest(".secaoInformacoeslogOut")
    ) {
      setEstiloInformacoes("secaoInformacoes");
      setAlternarIcone(false);
    }
  }


  const aoClicarForaMenuLateral = (event: MouseEvent) => {
    if (
      menuLateral.current &&
      !menuLateral.current.contains(event.target as Node) &&
      !(event.target as Element).closest(".hamburger-button")
    ) {
      setAbrirMenu(false);
    }
  };


  useEffect(() => {
    document.addEventListener("mousedown", aoClicarForaSecaoInformacoes);
    return () => {
      document.removeEventListener("mousedown", aoClicarForaSecaoInformacoes);
    };
  }, [estiloInformacoes]);


  useEffect(() => {
    document.addEventListener("mousedown", aoClicarForaMenuLateral);
    return () => {
      document.removeEventListener("mousedown", aoClicarForaMenuLateral);
    };
  }, [abrirMenu]);


  return {
    dadosUsuario,
    sair,
    componenteAtivo,
    setComponenteAtivo,
    abrirMenu,
    setAbrirMenu,
    menuLateral,
    alternarIcone,
    setAlternarIcone,
    estiloInformacoes,
    setEstiloInformacoes,
    linkAtivo,
    setLinkAtivo,
    manipularClickMenuDashboard,
    manipularClickMenuTerminalVendas,
    manipularClickRegistroVendas,
    manipularClickMenuProdutos,
    manipularClickExpedição,
    manipularClickAprovarExpedição,
    manipularClickPopupAdicionarProduto,
    manipularClickFecharPopup,
    abirPopupAdicionarProdutos,
    abrirSubmenuUsuarios,
    setAbrirSubmenuUsuarios,
    manipularClickMenuUsuarios,
    alterarIconeFuncao,
    mostrarSecaoInformacoes,
  }
};

export default useHome;
