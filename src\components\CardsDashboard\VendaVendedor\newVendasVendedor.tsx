import { PropsVendaVendedor } from "../../../interfaces";
import CardDash from "../card"

const NewVendaVendedor: React.FC<PropsVendaVendedor> = ({ dados }) => {
 
 const vendasPorVendedor: Record<
        string,
        { total: number; quantidade: number }
      > = {};

  dados?.forEach((pedido) => {
    const vendedores = Object.keys(vendasPorVendedor);
    if (vendedores.length > 5) {
      let menorVenda = vendedores[0];
      vendedores.forEach((vendedor) => {
        if (vendasPorVendedor[vendedor].total < vendasPorVendedor[menorVenda].total) {
          menorVenda = vendedor;
        }
      });
      if (vendasPorVendedor[menorVenda].total < parseFloat(pedido.total)) {
        delete vendasPorVendedor[menorVenda];
      }
    }

    if (vendasPorVendedor[pedido.vendedor]) {
      vendasPorVendedor[pedido.vendedor].total += parseFloat(pedido.total);
      vendasPorVendedor[pedido.vendedor].quantidade += 1;
    } else {
      vendasPorVendedor[pedido.vendedor] = {
        total: parseFloat(pedido.total),
        quantidade: 1,
      };
    }
    
  });


  // ordenar os vendedores pelo total de vendas
  const vendedoresOrdenados = Object.keys(vendasPorVendedor).sort((a, b) => vendasPorVendedor[b].total - vendasPorVendedor[a].total);

  if (Object.keys(vendasPorVendedor).length === 0) {
    return (
      <>
        <CardDash titulo="Venda por vendedor">
          <div className="divCardDashboard divCardVazio">
            <ul className="ulCardVazio">
              <li className="liCardVazio">
                Não há dados para exibir  
              </li>
            </ul>
          </div>
        </CardDash>
      </>
    )
  }

    return (
        <>
            <CardDash titulo="Venda por vendedor">
                <div className="divCardDashboard">
                    <ul className="ulCardDashboard">
                        {vendedoresOrdenados.map((vendedor) => (
                            <li
                                key={vendedor}
                                className="liCardDashboard"
                            >
                                {vendedor}:
                                <div className="divCardVendas">
                                    <span className="spanCardMonetario">
                                        {" "}
                                        <p><strong>{vendasPorVendedor[vendedor].quantidade}</strong>  vendas</p>
                                    </span>
                                    <span className="spanCardMonetario">
                                        {" "}
                                        R${vendasPorVendedor[vendedor].total.toFixed(2)}{" "}
                                    </span>
                                </div>
                            </li>
                        ))}
                    </ul>
                </div>
            </CardDash>
        </>
    )
}

export default NewVendaVendedor