import Cookies from "js-cookie";
import { toast } from "react-toastify";
import api from "../apiService";
import { useEffect, useState } from "react";
import { buscarProdutos } from "../pages/TerminalVendas/itensPedido/Produtos/buscarProdutos";
import { useAutenticacao } from "../contexts/autenticacao/AuthContext";
import { OpcaoSelecao } from "../pages/TerminalVendas/Utils/TerminalTypes";
import { v4 as uuidv4 } from "uuid";
import { ProdutoExpedicao } from "../interfaces";




const useTerminalExpedicao = () => {
    const { dadosUsuario } = useAutenticacao();
    const [produtoItem, setProdutoItem] = useState<OpcaoSelecao[]>([]);
    const [produtoSelecionado, setProdutoSelecionado] = useState<
      (number | null)[]
    >([]);
    const [produtoEnviado, setProduto] = useState<ProdutoExpedicao[]>([
      {
        id: "",
        nome: "",
        quantidade: 1,
      },
    ]);

  
    useEffect(() => {
      buscarProdutos(setProdutoItem);
    }, []);


  
    const limparCampos = () => {
      setProdutoSelecionado([]);
      setProduto([
        {
          nome: "",
          quantidade: 1,
        },
      ]);
    };


    
    const adicionarProduto = () => {
      setProduto([
        ...produtoEnviado,
        {
          id: uuidv4(),
          nome: "",
          quantidade: 1,
        },
      ]);
    };
  


    const removerProduto = (index: number) => {
      const novosProdutos = [...produtoEnviado];
      novosProdutos.splice(index, 1);
      setProduto(novosProdutos);
      const novosProdutosSelecionados = [...produtoSelecionado];
      novosProdutosSelecionados.splice(index, 1);
      setProdutoSelecionado(novosProdutosSelecionados);
    };
  
    useEffect(() => {
      const produtoExpedicao = document.getElementById("produtoExpedicao");
      if (produtoExpedicao?.classList.contains("bordaVermelha")) {
        produtoExpedicao?.classList.remove("bordaVermelha");
      }
    }, [produtoSelecionado]);
  

    
    const enviarExpedicao = async () => {
      try {
        if (produtoSelecionado.length === 0) {
          const produtoExpedicao = document.getElementById("produtoExpedicao");
  
          produtoExpedicao?.classList.add("bordaVermelha");
          toast.error("Selecione ao menos um produto");
          return;
        }
  
        const itensValidadosExpedicao = produtoEnviado.filter(
          (item) => typeof item.id
        );
  
        const dataExpedicao = {
          produtosEnviados: itensValidadosExpedicao.map((item, index) => ({
            produtoId: produtoSelecionado[index],
            quantidade: item.quantidade,
          })),
        };
  
        if (
          dataExpedicao.produtosEnviados.length === 0 ||
          dataExpedicao.produtosEnviados.some(
            (item) => item.produtoId === undefined
          )
        ) {
          toast.error("Selecione ao menos um produto");
          return;
        }
  
        if (
          dataExpedicao.produtosEnviados.some((item) => item.quantidade === 0)
        ) {
          toast.error("Selecione uma quantidade válida");
          return;
        }
  
        const response = await api.post("/expedicao", dataExpedicao, {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        });
  
        if (response.status === 200 || response.status === 201) {
          toast.success("Expedição enviada com sucesso");
          limparCampos();
        } else {
          console.warn("Resposta inesperada da API");
        }
      } catch (error) {
        toast.error("Erro ao enviar expedição");
        console.error(error);
      }
    };

    return {
        dadosUsuario,
        produtoItem,
        produtoSelecionado,
        produtoEnviado,
        setProdutoSelecionado,
        setProduto,
        adicionarProduto,
        removerProduto,
        enviarExpedicao,
    };
};

export default useTerminalExpedicao;