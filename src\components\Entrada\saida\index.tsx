import CardDash from "../../CardsDashboard/card";
import { FaArrowDown, FaArrowUp, FaBalanceScale } from "react-icons/fa";
import "./styles.css";

interface CardItem {
    tipo: "Entrada" | "Saída" | "Saldo";
    valor: number;
    icon: JSX.Element;
}

const CardItem: React.FC<CardItem> = ({ tipo, icon, valor }) => {
    return (
        <div className="card-entradas-saidas-item">
            <section
                style={{
                    display: "flex",
                    flexDirection: "column",
                    gap: "5px",
                    justifyContent: "center",
                    alignItems: "center",
                    width: "100%",
                }}
            >
                <div className="label-entradas-saidas">
                    {icon}
                    <span>{tipo}</span>
                </div>
                <h1>
                    {tipo === "Entrada" ? (
                        <span style={{ color: "green" }}> + {valor}</span>
                    ) : (
                        <>
                            {tipo === "Saída" ? (
                                <span style={{ color: "red" }}> - {valor}</span>
                            ) : (
                                <span style={{ color: "gray" }}>{valor}</span>
                            )}
                        </>
                    )}
                </h1>
            </section>
        </div>
    );
};

const CardEntradaSaida = ({ entradas, saidas }: { entradas: number; saidas: number }) => {
    return (
        <CardDash titulo="Entradas e Saídas de Portas">
            <main className="container-card-entradas-saidas">
                <CardItem tipo="Entrada" icon={<FaArrowUp />} valor={entradas} />
                <CardItem tipo="Saída" icon={<FaArrowDown />} valor={saidas} />
                <CardItem tipo="Saldo" icon={<FaBalanceScale />} valor={entradas - saidas} />
            </main>
        </CardDash>
    );
};

export default CardEntradaSaida;
