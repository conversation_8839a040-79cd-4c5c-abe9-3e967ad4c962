export interface Pedido {
    dataInicial: string;
    dataFinal: string;
    id: number;
    total: string;
    cliente: string;
    pagamento: string;
    criadoEm: string;
    vendedor: string;
    itensPedido: {
        descricao: string | null;
        quantidade: number;
        subtotal: string;
        desconto: string;
        produto: string;
    }[];
    parcelamento: number;
}

export interface DashboarI {
    pedidos: Pedido[];
    entradaPortas: number;
    saidaPortas: number;
}
