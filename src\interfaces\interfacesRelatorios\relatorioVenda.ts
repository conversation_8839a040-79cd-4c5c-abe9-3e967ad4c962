
export interface ItemPedidoRelatorioVendas {
    descricao: string;
    quantidade: number ;
    subtotal: string;
    desconto: string;
    produto: string;
  }
  
export interface DadosRelatorioVendas {
    id: number;
    total: string;
    cliente: string;
    pagamento: string;
    criadoEm: string;
    vendedor: string;
    itensPedido: ItemPedidoRelatorioVendas[];
    parcelamento: number;
  }