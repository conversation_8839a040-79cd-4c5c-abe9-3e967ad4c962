import ReactModal from "react-modal";
import React from "react";
import './modal.css';

import { IoClose } from "react-icons/io5";
import { ModalProps } from "../../interfaces";

ReactModal.setAppElement("#root"); 


const ModalFRP: React.FC<ModalProps> = ({
  esta<PERSON>berto,
  aoSolicitarFechar,
  children,
  style,
}) => {
  return (
		<ReactModal
			isOpen={estaAberto}
			onRequestClose={aoSolicitarFechar}
			overlayClassName="custom-modal-overlay"
			className="custom-modal"
			style={{ overlay: style }}
		>
			<div className="divBotaoFecharModal">
				<button
					className="botaoFecharModal"
					onClick={aoSolicitarFechar}
				>
					<IoClose />
				</button>
			</div>

			{children}
		</ReactModal>
  );
};

export default ModalFRP;
