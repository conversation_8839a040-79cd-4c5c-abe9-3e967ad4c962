import react from "@vitejs/plugin-react-swc";
import { defineConfig, loadEnv } from "vite";
import { VitePWA } from "vite-plugin-pwa";

export default defineConfig(({ mode }) => {
	const env = loadEnv(mode, process.cwd(), "");
	const HOST = env.VITE_HOST;

	return {
		preview: {
			port: 4175,
			allowedHosts: [HOST],
		},
		server: {
			port: 3001,
		},
		plugins: [
			react(),
			VitePWA({
				registerType: "autoUpdate",
				includeAssets: ["favicon.svg", "robots.txt"],
				manifest: {
					name: "<PERSON><PERSON><PERSON> Porma<PERSON>",
					short_name: "FRP",
					id: "/?source=pwa",
					orientation: "portrait",
					scope: "/login",
					start_url: "/login",
					display: "standalone",
					background_color: "#000",
					description: "Feirão Pormade",
					theme_color: "#ffffff",
					icons: [
						{
							src: "/maskable.png",
							sizes: "192x192",
							type: "image/png",
							purpose: "any maskable",
						},
						{
							src: "/logo196.png",
							sizes: "192x192",
							type: "image/png",
							purpose: "image/png",
						},
						{
							src: "/logo256.png",
							sizes: "256x256",
							type: "image/png",
							purpose: "image/png",
						},
						{
							src: "/logo384.png",
							sizes: "384x384",
							type: "image/png",
							purpose: "image/png",
						},
						{
							src: "/logo512.png",
							sizes: "512x512",
							type: "image/png",
							purpose: "image/png",
						},
					],
				},
			}),
		],
	};
});
