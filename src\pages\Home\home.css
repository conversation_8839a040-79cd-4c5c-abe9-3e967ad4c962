* {
    margin: 0;
    padding: 0;
    box-sizing: border-box; 
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
input[type=number]::-webkit-inner-spin-button { 
    -webkit-appearance: none;
}
.divConteudoContainer{
    height: 90%;
}
h2{
    margin: 3%;
    font-size: calc(1.325rem + .9vw);
    display: flex;
    justify-content: center;
    color: var(--second-color);
}
ul {
    margin-bottom: 0 ;
    padding-left: 0;
}
.main {
    width: 100%;
    height: 100vh;
    overflow: hidden;
}
.top-bar {
    background-color: var(--background-primary-color);
    position: relative;
    display: flex;
    height: 70px;
    justify-content: space-between;
    align-items: center;
    padding: 0 30px;
    border-bottom: 1px solid var(--primary-color);
}
.logOut {
    margin-left: 15px;
    width: 30px;
    height: 30px;
}
.hamburger-button {
    display: none;
}
svg{
    color: var(--second-color);
}
.logOutIcon{
    width: 30px;
    height: 30px;
}
.logoFRP{
    display: flex;
    justify-content: center;
}

.logoFRP img {
    width: 170px;
    height: 100%;
}
.content-container {
    display: flex;
    height: calc(100vh - 70px); 
}
.menu {
    height: calc(100vh - 70px); 
    flex: 0 0 250px;
    width: 250px;
}
/* .menuPrincipal{
    z-index: 10 !important;
} */
.menuLateral {
    height: 100%;
    display: flex;
    background-color: var(--background-primary-color);
    width: 100%;
    color: var(--second-color);
    flex-direction: column;
    align-items: flex-start;
    justify-content: space-between;
}
.menuLista {
    list-style-type: none; 
    width: 100%;
}
.menuLista li {
    font-size: 18px; 
    height: auto;
    width: 100%;
    padding: 5%;
}
.menuLista li:hover{
    background-color: var(--primary-color);
    transition: 0.5s;
}
.menuLista li ul {
    overflow: hidden;
    max-height: 0;
    transition: max-height 0.3s ease;
}
.menuLista li ul {
    max-height: 100px;
    width: 105.5%;
    list-style: none;
    padding-left: 10%;
    margin-top: 12px;
}
.menuLista li ul li{
    border-radius: 3px;
}
.menuLista li:hover ul li:hover{
    background-color: var(--primary-color-up-variant);
    transition: 0.9s;
}
.menuLista li ul a{
    font-size: 15px;
}
.menuLista a {
    text-decoration: none; 
    color: inherit; 
}
.Conteudo {
    flex: 1;
    background-color: var(--background-second-color); 

}
.active {
    background-color: var(--primary-color);
}
.conteudo-container {
    max-height: calc(100vh - 60px);
    overflow-y: scroll;
    overflow-x: hidden;
}
.cursor:hover{
    cursor: pointer;
}
.name {
    color: var(--second-color);
}

#nomeUsuario {
    color: var(--second-color);
    margin-bottom: 0;
    white-space: nowrap;
}

#roleUsuario {
    color: var(--primary-color);
    margin: 0;
}
.top-bar .secaoInformacoes {
    display: flex;
    align-items: center;
}
.iconPersonDown{
    color: var(--second-color);
    width: 30px;
    height: 30px;
}
.botaoAbrirSubMenuUsuario {
    display: none;
    background-color: transparent;
    border: none;
}
.secaoInformacoeslogOut{
    margin-left: 15px;
}
.paragrafoVersao{
    margin: 0 0 8px 10px;
    color: #6e6e6e;
    font-weight: bold;
    font-size: 14px;
    font-style: italic;
}
 @media screen and (max-width: 875px) {
    .hamburger-button {
      display: block;
      margin: 18px 25px;
      background: none;
      border: none;
    }
    .hamburgerIcon{
        width: 20px;
        height: 20px;
    }
    .menu {
      display: none;
    }
    .menu.open {
      width: 30vw;
      display: block;
      position: absolute;

    }
    .top-bar{
        padding: 0;
        position: relative;
        z-index: 1;
    }
    svg{

        color: var(--second-color);
    }
    .logoFRP{
        position: absolute;
        right:  50vw;
        left: 50vw;
    }
    .secaoInformacoes{
        padding: 0;
        margin: 0;
        position: relative;
        display: none !important;
        flex-direction: column;
        z-index: 2;
        opacity: 0;
    }
    .mostraSecaoInformacoes{
        position: relative;
        top:100px;
        transition: 0.5s;
        padding: 5px;
        opacity: 1;
        z-index: 2;
        background-color: var(--background-primary-color);
        border-bottom-left-radius: 5px;
        border: 1px solid var(--primary-color);
        border-right: 1px solid var(--background-primary-color);
    }
    .secaoInformacoesUsuario{
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 150px;
        height: 70px;
        padding: 10px;
    }
    .secaoInformacoeslogOut{
        display: flex;
        align-items: center;
        justify-content: center;
        border-top: 1px solid var(--second-color);
        width: 150px;
        height: 50px;
        margin-left: 0;
    }
    .botaoAbrirSubMenuUsuario {
        display: flex;
        position: absolute;
        top: 20px;
        right: 20px;
        z-index: 10;
    }
}
@media screen and (max-width: 735px) {
    .menu.open {
        width: 40vw;
    }
}

@media screen and (max-width: 880px) {
    .menu.open {
       z-index: 10;
    }
    .mostraSecaoInformacoes {
       z-index: 20;
    }
    
}

@media screen and (max-width: 551px) {
    .menu.open {
        width: 60vw;
        z-index: 10;
    }

}
@media screen and (max-width: 368px) {
    .menu.open {
        width: 100vw;
        z-index: 10;    }
}
.cardNomeUsuario{
    color: var(--background-primary-color);
}