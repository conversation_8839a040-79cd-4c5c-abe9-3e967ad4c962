import React, { useState } from 'react';

import <PERSON>ie from 'js-cookie';
import api from '../../apiService';
import { toast } from 'react-toastify';
import './style.css';
import ModalFRP from '../Modal';
import { ErroAxios } from '../../interfaces';



const PopupAdicionarProdutos: React.FC<PropriedadesPopupProduto> = ({ mostrar, fechar }) => {
  const [nomeProduto, definirNomeProduto] = useState('');
  const [precoProduto, definirPrecoProduto] = useState<number | string>('0');
  const [quantidadeProduto, definirQuantidadeProduto] = useState<number | string>(0);
  const [editando, definirEditando] = useState(false);
  

  const validarProduto = (): boolean => {
    
    if (!nomeProduto.trim()) {
      toast.dismiss();
      toast.error("Nome do produto não pode ser vazio");
      return false;
    }

    const precoParsed = parseFloat(precoProduto.toString());
    if (isNaN(precoParsed) || precoParsed <= 0) {
      toast.dismiss();
      toast.error("O valor precisa ser maior que zero");
      return false;
    }
   
    return true;
  };



  const manipularMudancaNome = (event: React.ChangeEvent<HTMLInputElement>) => {
    definirNomeProduto(event.target.value);
  };

  const manipularMudancaPreco = (event: React.ChangeEvent<HTMLInputElement>) => {
    let valor = event.target.value;
    valor = valor.replace(',', '.');
    const preco = parseFloat(valor);
    if (!isNaN(preco) || valor === '') {
      definirPrecoProduto(valor);
    }
  };

  const manipularMudancaQuantidade = (event: React.ChangeEvent<HTMLInputElement>) => {
    const valor = event.target.value;
    if (!isNaN(Number(valor)) || valor === '') {
      definirQuantidadeProduto(valor);
    }
  };

  const manipularFocoPreco = () => {
    definirEditando(true);
  };

  const manipularBlurPreco = () => {
    definirEditando(false);
    if (precoProduto === '') {
      definirPrecoProduto('0');
    }
  };

  const limparCampos = () => {
    definirNomeProduto('');
    definirPrecoProduto('0');
    definirQuantidadeProduto(0);
  };


  const manipularEnvio = async () => {
    if (!validarProduto()) return;
    const token = Cookie.get("token");

    try {
      const resposta = await api.post('/produtos', {
        nome: nomeProduto,
        valor: parseFloat(precoProduto.toString()),
        quantidade: parseInt(quantidadeProduto.toString(), 10) 
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : undefined
        }
      });

      if (resposta.status === 201) {
     
        setTimeout(() => {
          toast.dismiss();
          toast.success('Produto adicionado com sucesso!');
            fechar();
            limparCampos();
           
        }, 0);
      }
    } catch (erro) {
      const erroAxios = erro as ErroAxios;
      if (erroAxios.response && erroAxios.response.status >= 400 && erroAxios.response.status < 500) {
        toast.dismiss();
        toast.error('Erro ao adicionar o produto');
      } else {
        toast.dismiss();
        toast.error('Erro ao adicionar o produto');
      }
    }
  };

  const precoFormatado = editando
    ? precoProduto.toString()
    : new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(Number(precoProduto));

    return (
      <>

			<ModalFRP estaAberto={mostrar} aoSolicitarFechar={fechar}>
				<h1>Adicionar Produto</h1>
				<div>
					<form>
						<div className="divInputPopupAdicionarProduto">
							<div className='divNomePopupAdicionarProduto'>
								<div id="inputNome">
									<label htmlFor="nomeProduto">
										Nome do Produto:
										<input
											type="text"
											id="nomeProduto"
											className="inputAdicionarProduto"
											placeholder="Insira o nome do produto"
											value={nomeProduto}
											onChange={manipularMudancaNome}
										/>
									</label>
								
								</div>
							</div>

							<div className="divPrecoQuantidade">
								<div className="divInputPrecoPopupAdicionarProduto">
									<label>Preço: </label>
									<input
										type="text"
										className="inputPrecoAdicionarProduto"
										value={precoFormatado}
										onChange={manipularMudancaPreco}
										onFocus={manipularFocoPreco}
										onBlur={manipularBlurPreco}
										placeholder="Insira o preço do produto"
									/>
								
								</div>
								<div className="divInputQuantidade">
									<label>Quantidade: </label>
									<input
										type="text"
										className="inputAdicionarQuantidade"
										value={quantidadeProduto.toString()}
										onChange={manipularMudancaQuantidade}
										placeholder="Insira a quantidade"
									/>
									
								</div>
							</div>
						</div>

						<div className="divBotaoAdicionarProduto">
							<button
								className="botaoAdicionarProduto"
								onClick={(e) => {
                  e.preventDefault();
                  manipularEnvio();
                }}
							>
								Adicionar
							</button>
						</div>
					</form>
				</div>
			</ModalFRP>
		</>
	);
};

export default PopupAdicionarProdutos;
