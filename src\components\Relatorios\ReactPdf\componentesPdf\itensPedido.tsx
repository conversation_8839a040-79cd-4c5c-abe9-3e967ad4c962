import { StyleSheet, Text, View } from "@react-pdf/renderer";
import { ItemPedidoRelatorioVendas } from "../../../../interfaces";
import Tabela from "./tabela";

export const ItensPedido = ({ itens, total, index }: { itens: ItemPedidoRelatorioVendas[]; total: string; index: number }) => {
	return (
		<>
			<View style={ItensPedidosEstilo.itensCampo}>
				<View style={ItensPedidosEstilo.container}>
					<Tabela itens={itens} />
				</View>

				<Text style={ItensPedidosEstilo.avisoGarantia}>
					Confira os materiais no ato da compra. Produtos no feirão não têm garantia ou troca.
				</Text>

				<View style={ItensPedidosEstilo.footer}>
					<View style={ItensPedidosEstilo.paginacaoView}>
						<Text style={ItensPedidosEstilo.paginacaoText}>{index + 1}</Text>
					</View>
					<Text style={ItensPedidosEstilo.total}>Total: {total}</Text>
				</View>
			</View>
		</>
	);
};

const ItensPedidosEstilo = StyleSheet.create({
	itensCampo: {
		width: "100%",
		height: 300,
	},
	container: {
		width: "100%",
		height: 300,
		display: "flex",
		justifyContent: "center",
		alignItems: "center",
		flexDirection: "column",
	},
	footer: {
		width: "100%",
		height: "15%",
		display: "flex",
		justifyContent: "flex-end",
		flexDirection: "row",
		position: "relative",
	},
	paginacaoView: {
		width: 10,
		display: "flex",
		justifyContent: "center",
		alignItems: "center",
		height: 10,
		backgroundColor: "gray",
		borderRadius: 5,
		position: "absolute",
		bottom: 0,
	},
	paginacaoText: {
		fontSize: 10,
		textAlign: "right",
		position: "absolute",
		bottom: 0,
		color: "white",
	},
	total: {
		fontSize: 14,
		textAlign: "right",
	},
	avisoGarantia: {
		fontSize: 11,
		display: "flex",
		justifyContent: "center",
		alignItems: "center",
		marginTop: 10,
		padding: 5,
		borderRadius: 3,
		color: "#b91c1c",
		backgroundColor: "#fef2f2",
		width: "100%",
		textAlign: "center",
	},
});
