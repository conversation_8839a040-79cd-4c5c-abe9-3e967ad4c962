import { OpcaoSelecao, Produto } from "../../geral/TerminalTypes";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import api from "../../../../apiService";
import Cookies from "js-cookie";

export const buscarProdutos = async (
  setProdutos: React.Dispatch<React.SetStateAction<OpcaoSelecao[]>>
) => {
  try {
    const resposta = await api.get("/produtos", {
      headers: {
        Authorization: `Bearer ${Cookies.get("token")}`,
      },
    });
    const produtoOptions = resposta.data.map((p: Produto) => ({
      id: p.id,
      label: p.nome,
      valor: p.valor,
    }));
    setProdutos(produtoOptions);
  } catch (error) {
    toast.error(`Erro ao buscar produtos ${error}`, {
      position: "top-right",
      autoClose: 5000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
      theme: "dark",
    });
  }
};

export {};
