.dashboard-container {
  max-width: 100%;
  padding: 10px;
  overflow-x: hidden;
}

.flex-equal-height>[class*='col-'] {
  display: flex;
  flex-direction: column;
}

.flex-equal-height>[class*='col-']>.card {
  flex: 1;
}

.cardGrafico {
  background-color: #000;
  border: 1px solid var(--primary-color);
  border-radius: 5px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  margin-top: 20px;
}

.card-header-custom {
  background-color: #28a745;
  color: #fff;
  padding: 10px;
}

.card-body-custom {
  padding: 25px;
  background-color: var(--background-primary-color);
  border: 1px solid var(--primary-color);
  color: var(--second-color);
  text-decoration: none;
  list-style: none;
}

.card-body-custom ul li {
  list-style: none;
}

.corpoDivDashboard {
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-header {
  border: none;
}

.card-header:hover {
  border: none;
}

.list-group {
  padding: 5px;
}

.botaoFiltro {
  background-color: var(--primary-color);
  border: var(--primary-color);
}

.botaoFiltro:hover {
  background-color: var(--primary-color-down-variant);
  border: var(--primary-color-down-variant);
}

.botaoFiltro:active {
  background-color: var(--primary-color-down-variant) !important;
  border: var(--primary-color-down-variant) !important;
}

#FormaPagemento {
  background-color: transparent;
  border: var(--primary-color);
}

.card .custom-card {
  border-radius: 15px;
}

.botaoGrafico:active {
  background-color: var(--primary-color-down-variant) !important;
  border: var(--primary-color-down-variant) !important;
}

.botaoGrafico {
  background-color: var(--primary-color);
  border: var(--primary-color);
  width: 100%;
  height: 38px;
  margin: 0 10px 0 0;
  border-radius: 5px;
  color: #fff;
}

.card-title-dash {
  color: white !important;
  font-size: 20px;
}

.mt-4 {
  margin-top: 0px !important;
}

.datasDashboard {
  width: 100%;
  margin: 20px 0 50px 0;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  flex: 1 1;
  flex-wrap: wrap;
  align-content: center;
}


.divFiltroDashboard {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  justify-content: flex-start;
  align-items: baseline;
  flex-direction: row;
}

.btnGerarRelatorio {
  background-color: var(--primary-color);
  border: var(--primary-color);
  width: 98%;
  height: 38px;
  margin: 0 10px 0 0;
  border-radius: 5px;
  color: #fff;
  margin-left: 1px;
}

.btnGerarRelatorio:hover {
  background-color: var(--primary-color-down-variant);
  border: var(--primary-color-down-variant);
}

.btnGerarRelatorio:active {
  background-color: var(--primary-color-down-variant) !important;
  border: var(--primary-color-down-variant) !important;
}

.divGerarRelatorio {
  align-self: flex-end;
  margin-left: auto;
  margin-right: 0;
}

.mostrarPor {
  display: flex;
  flex-direction: row;
  width: 40%;
  margin: 30px 0px 30px 0px;
}

.labelMostrarPor {
  color: var(--second-color);
  height: auto;
  width: 30%;
  margin: 0 10px 0 0;
  display: flex;
  align-items: center;
}

.botaoGrafico {
  background-color: var(--primary-color);
  border: var(--primary-color);
}

.botaoGrafico:hover {
  background-color: var(--primary-color-down-variant);
  border: var(--primary-color-down-variant);
}

.cardDashboad {
  width: 25%;
  padding: 6px;
}


.flex-equal-height>[class*='cardDashboad'] {
  display: flex;
  flex-direction: column;
}

.flex-equal-height>[class*='cardDashboad']>.card {
  flex: 1;
}

.PaddingDashboard {
  padding: 0;
  width: 20%;
}

@media screen and (max-width: 768px) {
  .PaddingDashboard {
    padding: 0;
    width: 100%;
    margin: 0 0 3px 0;
  }

  .cardDashboad {
    width: 100%;
  }

}

.PaddingDireita {
  padding: 0 10px 0 0;
}

.formFiltroDashboard {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  flex: 1 1;
  flex-wrap: wrap;
  align-content: center;
}

.labelDashboard {
  width: 98%;
  margin: 0 auto;
  color: #fff;
}

@media (max-width: 1800px) {
  .cardDashboad {
    width: 50%;
  }
}

@media (max-width: 768px) {
  .cardDashboad {
    width: 100%;
  }

  .PaddingDireita {
    padding: 0;
  }
}

.divNaoHaDadosGrafico {
  display: flex;
  justify-content: center;
  height: 100%;
  width: 100%;
  flex-direction: column;
  align-items: center;
  margin: 25px 10px;
}

.divNaoHaDadosGrafico>p {
  color: var(--second-color);
  font-size: 20px;
}

.iconeNaoHaDadosGrafico {
  margin-top: 30px;
  margin-bottom: 5px;
  color: var(--second-color);
  font-size: 100px;
}

.tamanhoMaximo {
  width: 100%;
  height: 100
}

.custom-card {
  background-color: var(--background-primary-color);
  color: var(--secondo-color);
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(26, 26, 26, 0.1);
  transition: transform .2s ease-in-out;
  margin-bottom: 3
}


.custom-card:hover {
  transform: scale(1.05);
}


.custom-card:hover {
  transform: scale(1.05);
}

.card-header-custom {
  background-color: var(--primary-color);
  color: var(--secondo-color);
  position: relative;
  max-width: 100%;
  display: grid;
}


.quantidadeDeVezesPagamento {
  border-bottom: 1px solid var(--primary-color); 
}


.textoNaoHaDados {
  font-size: 20px;
  color: #d9534f;
}

.cardProduto>[class*='cardAltura'] {
  display: flex;
  flex-direction: column;
}

.cardProduto>[class*='cardAltura']>.card {
  flex: 1;
}

.divPaginacaoGrafico {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30px;
}


.paginaAtualGrafico {
  color: var(--second-color);
  font-size: 20px;
  color: var(--primary-color);
  text-shadow: #fff 0px 0px 1px;
}

.botaoGraficoDashboard {
  background-color: var(--primary-color);
  border: none;
  width: 100px;
  height: 40px;
  color: #fff;
  font-size: 18px;
  margin: 0 10px 0 0;
  padding: 5px 10px;
  cursor: pointer;
  border-radius: 5px;

}

.botaoGraficoDashboard:hover {
  background-color: var(--primary-color-down-variant);
  border: var(--primary-color-down-variant);
}

.botaoGraficoDashboard:active {
  background-color: var(--primary-color-down-variant);
  border: var(--primary-color-down-variant);
}

.botaoGraficoDashboard:disabled {
  background-color: var(--botao-desativado-cor);
  border: var(--primary-color-down-variant);
  cursor: not-allowed;
}

.rowDataDashboard {
  display: flex;
  flex-direction: row;
  width: 100%;
  justify-content: space-around;
  flex-wrap: wrap;
  align-content: flex-end;
  align-items: center;
}

.campoDoInputDashboad {
  width: 100%;
  outline: none;
}


.paginaAtualGrafico p {
  font-size: 18px;
  margin: 0;
}

.tituloSelectDashboard {
  color: var(--second-color);
  height: auto;
  margin-bottom: 5px;
  display: flex;
  align-items: center;
}

.btnGerarRelatorio:disabled {
  background-color: var(--botao-desativado-cor);
  border: var(--primary-color-down-variant);
}

.botaoMaisPorPagina {
  background-color: var(--primary-color);
  width: 110px;
  height: 44px;
  color: #fff;
  border: none;
  padding: 8px 16px;
  font-size: 16px;
  cursor: pointer;
  border-radius: 5px;
  margin-bottom: 20px;
}


.botaoMaisPorPagina:hover {
  background-color: var(--primary-color-down-variant);
}

.chart-container {
  background-color: #000;
  padding: 10px;
  border-radius: 5px;
}

.divQuantidadePorPagina {
  text-align: center;

  display: flex;
  flex-direction: row;
  width: 100%;
  justify-content: flex-end;
  margin: 0 0px 30px 0px;
}

.maisPorPagina {
  width: auto;
  height: 40px;
  margin: 0 10px 0 0;
  border-radius: 5px;
}

.botaoAlterarPorPagina {
  background-color: var(--primary-color);
  border: var(--primary-color);
  width: 100px;
  height: 40px;
  margin: 0 10px 0 0;
  border-radius: 5px;
}

.botaoAlterarPorPagina:hover {
  background-color: var(--primary-color-down-variant);
  border: var(--primary-color-down-variant);
}

.textoAdicionarPorPagina {
  color: var(--second-color);
  text-decoration: none;
  text-shadow: var(--primary-color-down-variant) 0px 0px 1px;
}

.baixarRelatorio {
  background-color: var(--primary-color);
  border: var(--primary-color);
  width: 50px;
  height: 50px;
  margin-top: 10px;
  border-radius: 5px;
}

.baixarRelatorio:hover {
  background-color: var(--primary-color-down-variant);
  border: var(--primary-color-down-variant);
}

.baixarRelatorio:active {
  background-color: var(--primary-color-down-variant);
  border: var(--primary-color-down-variant);
}

.divBaixarPDF {
  display: flex;
  justify-content: flex-end;
}

.divVisualizacaoPDF {
  width: 100%;
  height: 60vh;
}

.inputFiltroDashboard {
  width: 100%;
  height: 40px;
  border-radius: 5px;
  border: 1px solid var(--primary-color);
  background-color: #fff;
  color: #000;
  padding: 0 10px 0 10px;
  outline: none;
}

.flex-equal-height {
  display: flex;
  flex-wrap: wrap;
}

.mt-4 {
  margin-top: 1rem;
}

/* .text-danger {
  flex: 0 0 33.333333%;
  max-width: 33.333333%
} */

/* .custom-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 4;
} */

.custom-modal-overlay {
  display: flex;
  align-items: center;
  justify-content: center;
}



.quantidadeDeVezes strong {
  color: #fff;
  background-color: var(--primary-color);
  border-radius: 5px;
}

.quantidadeDeVezesPagamento {
  width: 100%; 
  height: 100%;
  display: flex;
  justify-content: space-between
}

.botaoRelatorioGeral {
  right: 0;
}

.btnFiltrarDashboard {
  background-color: var(--primary-color);
  border: var(--primary-color);
  width: 100%;
  height: 38px;
  margin: 0 10px 0 0;
  border-radius: 5px;
  color: #fff;
}

.botaoFiltrar {
  background-color: var(--primary-color);
  border: var(--primary-color);
  width: 100%;
  height: 38px;
  margin: 24px 10px 0 0;
  border-radius: 5px;
  color: #fff;
}


.divParaFiltrar {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 80%;
}

.react-datepicker {
  background-color: #fefefe;
  border: 1px solid ⁠ddd;
}

.react-datepickerheader {
  background-color: #f0f0f0;
}

.react-datepickerday--selected {
  background-color: #4caf50;
}

#inputDataDashboard {
  width: 50%;
}

#inputData {
  width: 100%;
}

.react-datepicker-wrapper {
  width: 100%;
}

/* .react-datepicker {
  width: 100%;
  
  border: 1px solid var(--primary-color);
}

.react-datepicker__month-container {
  background-color: black;
  color: #fff;
}

.react-datepicker__header {
  text-align: center;
  background-color: #000000;
  color: white;
  border-bottom: 1px solid var(--primary-color);
  border-top-left-radius: 0.3rem;
  padding: 8px 0;
  position: relative;

} */

.filtros-divs  {
  width: 25%;
  display: flex;
  justify-content: space-evenly;
  margin-bottom: 10px;
  align-items: center;
  flex-direction: column;
  align-content: flex-start;
 
}

.selectDashboard-filtro {
  width: 100%;
  text-wrap: nowrap;
  font-weight: bold;
  color: #000;
}

.selectDashboard-filtros-pagamento {
  width: 98%;
  text-wrap: nowrap;
  color: #000;
  font-weight: bold;
}

.botao-gerar-relatorio-div-filtros {
  display: flex;
  
  align-items: end;
  width: 100%;
  min-height: 38px;
}

@media screen and (max-width: 1400px) {
  .filtros-divs {
    width: 50%;
    display: flex;
    justify-content: space-evenly;
    margin-bottom: 10px;
    align-items: center;
    flex-direction: column;
    flex-wrap: wrap;
    align-content: flex-start;
  }

}

@media screen and (max-width: 780px) {
  .filtros-divs {
    width: 100%;
  }

  .labelDashboard {
    width: 100%;
  }
  
  .selectDashboard-filtros-pagamento {
    width: 100%;
  }

  .btnGerarRelatorio {
    width: 100%;
  }

 
}

.css-1dimb5e-singleValue {
  color: #000 !important;
}


@media screen and (max-width: 551px) {

     .mostrarPor {
         width: 100%;
     }
  
}

@media screen and (max-width: 1400px) {
  .btnGerarRelatorio {
    width: 100%;
  }
}