import { useEffect, useState } from "react";
import { OpcaoParcelas } from "../pages/TerminalVendas/Utils/TerminalTypes";
import Cookies from "js-cookie";
import api, { baseURL } from "../apiService";
import { toast } from "react-toastify";
import { AxiosError } from "axios";
import { PedidoRegistro } from "../interfaces";
import { da } from "date-fns/locale";

const useRegistroVendas = () => {
	const [pedidos, setPedidos] = useState<PedidoRegistro>();
	const [status, setStatus] = useState<string>("Pendente");
	const [quantidadeFiltro, setQuantidade] = useState(10);
	const [ordenar, setOrdenar] = useState<string>("DESC");
	const [recarregar, setRecarregar] = useState<boolean>(false);
	const [dataInicial, setDataInicial] = useState<Date | null>(null);
	const [dataFinal, setDataFinal] = useState<Date | null>(null);

	const [urlImagem, setUrlImagem] = useState<string | null>(null);
	const [pdfPedidoId, setPdfPedidoId] = useState<number | null>(null);
	const [confirmModalShow, setConfirmModalShow] = useState(false);
	const [modalShow, setModalShow] = useState(false);
	const [confirmPedidoId, setConfirmPedidoId] = useState<number | null>(null);
	const [modalImagemShow, setModalImagemShow] = useState(false);
	const [paginaAtual, setPaginaAtual] = useState<number>(1);

	const [motivoCancelamento, setMotivoCancelamento] = useState<string>("");
	const [mostrarCampoMotivo, setMostrarCampoMotivo] = useState<boolean>(false);
	const [carregamento, setCarregamento] = useState(true);

	const [opcaoSelecionada, setOpcaoSelecionada] = useState<OpcaoParcelas | null>({ value: 10, label: "10" });
	const [exibirMotivoAberto, setExibirMotivoAberto] = useState<number | null>(null);

	const manipularConfirmacao = async (id: number, status: string) => {
		const token = Cookies.get("token");
		const url = `/pedidos/status/${id}`;

		let data = {};
		if (motivoCancelamento !== "") {
			data = {
				status,
				cancelamento: motivoCancelamento,
			};
		} else {
			data = {
				status,
			};
		}
		try {
			const resposta = await api.put(url, data, {
				headers: {
					Authorization: `Bearer ${token}`,
					"Content-Type": "application/json",
				},
			});

			if (resposta.status === 200 || resposta.status === 204) {
				toast.success(`${resposta.data.message}`);
				setRecarregar(true);

				setConfirmModalShow(false);
				setMostrarCampoMotivo(false);
				setMotivoCancelamento("");
			}
		} catch (erro) {
			toast.error("Erro ao atualizar status do pedido", {
				toastId: "erro",
			});
		}
	};

	const BuscarPedidosFiltrados = async () => {
		const quantidadePedidosAtuais = pedidos?.pedidos.length || 0;

		try {
			const token = Cookies.get("token");
			const data = {
				pagina: paginaAtual,
				quantidade: quantidadeFiltro,
				status: status,
				filtro: ordenar,
				dataInicial: dataInicial ? dataInicial.toISOString() : null,
				dataFinal: dataFinal ? dataFinal.toISOString() : null,
			};

			const resposta = await api.post(
				"/pedidos/lista",
				data,

				{
					headers: {
						Authorization: `Bearer ${token}`,
					},
				}
			);

			setPedidos(resposta.data);

			const quantidadeNecessaria = quantidadePedidosAtuais - resposta.data.pedidos.length;

			if (quantidadeNecessaria < 0) {
				return;
			}

			if (quantidadeNecessaria >= quantidadePedidosAtuais || quantidadeNecessaria === null) {
				return;
			} else {
				// verificar se é uma quantidade de array não invalida
				if (quantidadeNecessaria < 0) {
					return;
				}

				const criarCardsTemporarios = Array(quantidadeNecessaria).fill({
					id: 0,
					total: "",
					cliente: "",
					pagamento: "",
					parcelamento: 0,
					criadoEm: "",
					vendedor: "",
					cancelamento: null,
					status: "falso",
				});

				setPedidos({
					...resposta.data,
					pedidos: [...resposta.data.pedidos, ...criarCardsTemporarios],
				});

				const quantidadeDePedidos = pedidos?.pedidos.length || 0;

				let timeout = 10;
				switch (quantidadeDePedidos as number) {
					case 10:
						timeout = 500;
						break;

					case 20:
						timeout = 1000;
						break;

					case 50:
						timeout = 2000;
						break;
				}

				setTimeout(() => {
					setPedidos({
						...resposta.data,
						pedidos: resposta.data.pedidos.filter((pedido: any) => pedido.id !== 0),
					});
				}, timeout);
			}

			return resposta.data;
		} catch (error) {
			console.error(error);
		} finally {
			setCarregamento(false);
			setRecarregar(false);
		}
	};

	const sincronizar = () => {
		BuscarPedidosFiltrados();
		toast.success("Sincronizado com sucesso", {
			toastId: "sucessoAoSincronizar",
		});
	};

	const buscarImagem = async (pedidoId: number) => {
		const token = Cookies.get("token");
		try {
			const resposta = await api.get(`/pedidos/imagem/${pedidoId}`, {
				headers: {
					Authorization: `Bearer ${token}`,
				},
			});
			if (resposta.data && resposta.data) {
				setUrlImagem(`${baseURL}/${resposta.data}`);
			} else {
				throw new Error("Data unexpected");
			}
		} catch (error) {
			const erro = error as AxiosError;
			if (erro.response && erro.response.status === 404) {
				toast.error(`Pedido sem imagem cadastrada`, {
					toastId: "erroImagem",
				});
			} else if (erro.response && erro.response.status === 500) {
				toast.error(`Erro interno`, {
					toastId: "erroInterno",
				});
			} else {
				toast.error(`Erro desconhecido`, {
					toastId: "erroDesconhecido",
				});
			}
		}

		setModalImagemShow(true);
	};

	const handleAbrirPDF = (pedidoId: number) => {
		setPdfPedidoId(pedidoId);
		setModalShow(true);
	};

	const modificarPagina = (pagina: number) => {
		setPaginaAtual(pagina);
		setTimeout(() => {
			document.querySelector("#conteudoContainer")?.scrollTo({ top: 0, left: 0, behavior: "smooth" });
		}, 0);
	};

	useEffect(() => {
		const isMobile = window.innerWidth < 768;

		const removePlaceholder = () => {
			const placeholder = document.querySelector(".placeholder");
			if (placeholder) {
				placeholder.remove();
			}
		};

		if (isMobile) {
			const conteudoContainer = document.querySelector("#conteudoContainer");
			conteudoContainer?.addEventListener("scroll", removePlaceholder);

			return () => {
				conteudoContainer?.removeEventListener("scroll", removePlaceholder);
			};
		}
	}, []);

	const abrirExibirMotivo = (indice: number) => {
		if (exibirMotivoAberto === indice) {
			setExibirMotivoAberto(null);
		} else {
			setExibirMotivoAberto(indice);
		}
	};

	const identificarIframeEAbrirPDF = () => {
		const div = document.querySelector(".divVisualizarRelatorioPedido") as HTMLElement;
		const iframe = div.querySelector("iframe") as HTMLElement;

		const src = iframe.getAttribute("src");

		if (src) {
			const win = window.open(src, "_blank");
			win?.focus();
		}
	};

	const ultimaPagina = pedidos?.total ? Math.ceil(pedidos?.total / quantidadeFiltro) : 1;

	return {
		pedidos,
		status,
		setStatus,
		quantidadeFiltro,
		setQuantidade,
		ordenar,
		setOrdenar,
		recarregar,
		setRecarregar,
		urlImagem,
		setUrlImagem,
		pdfPedidoId,
		setPdfPedidoId,
		confirmModalShow,
		setConfirmModalShow,
		modalShow,
		setModalShow,
		confirmPedidoId,
		setConfirmPedidoId,
		modalImagemShow,
		setModalImagemShow,
		paginaAtual,
		setPaginaAtual,
		motivoCancelamento,
		setMotivoCancelamento,
		mostrarCampoMotivo,
		setMostrarCampoMotivo,
		carregamento,
		setCarregamento,
		opcaoSelecionada,
		setOpcaoSelecionada,
		exibirMotivoAberto,
		setExibirMotivoAberto,
		manipularConfirmacao,
		BuscarPedidosFiltrados,
		sincronizar,
		buscarImagem,
		handleAbrirPDF,
		modificarPagina,
		abrirExibirMotivo,
		identificarIframeEAbrirPDF,
		ultimaPagina,
		dataInicial,
		setDataInicial,
		dataFinal,
		setDataFinal,
	};
};

export default useRegistroVendas;
