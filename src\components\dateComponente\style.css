.custom-datepicker-input {
    padding: 10px;
    width: 99%;
    min-height: 38px;
    border: 1px solid white;
    background-color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    outline: none;
    cursor: pointer;
    border-radius: 5px;
    
  }

  @media screen and (max-width: 1400px) {
    .custom-datepicker-input {
      width: 98%;
    }
    
  }

  @media screen and (max-width: 780px) {
    .custom-datepicker-input {
      width: 100%;
    }
    
  }
  
  .custom-datepicker-p {
    color: black;
    width: 80%;
    font-size: 15px;
    text-wrap: nowrap;
    text-align: start;
    font-weight: bold;
  }


  .calendarIconPicker {
    color: black;
  }
  .custom-datepicker-input:active {
    outline: none;
  }
  
  .custom-datepicker-input:hover {
    outline: none;
  
  }
  
  .custom-datepicker-input:focus {
    outline: none;
  }
  
  .custom-datepicker-input svg {
    margin-left: 10px;
  }

  
  .react-datepicker {
    background-color: black;
    border-color: green;
  }
  
  .react-datepicker__view-calendar-icon input {
    padding: 6px 15px 5px 25px;
    text-align: inherit;
  }
  
  .react-datepicker__header {
    background-color: black;
    border-bottom: 1px solid green;
    position: relative;
    padding-top: 2em;
  }
  
  .react-datepicker__day, .react-datepicker__day-name {
    color: white;
  }
  
  .react-datepicker__day--outside-month {
    color: #555;
  }
  
  .react-datepicker__current-month, .react-datepicker__header__dropdown--select {
    color: green;
  }
  
  .react-datepicker__navigation {
    top: 20px;
    /* filter: invert(0.75); */
    color: green;
  }
  
  .react-datepicker__day--selected,
  .react-datepicker__day--in-range,
  .react-datepicker__day--in-selecting-range,
  .react-datepicker__day--keyboard-selected {
    background-color: darkgreen;
    color: white;
  }
  
  .react-datepicker__day:hover {
    background-color: darkgreen;
    color: white;
  }
  
  .react-datepicker__navigation-icon {
    /* filter: invert(1); */
  }
  
  .react-datepicker__navigation {
    position: absolute;
    top: 0;
    /* filter: invert(0.75); */
    color: green;
  }
  
  .react-datepicker__navigation--previous {
    left: 10px;
  }
  
  .react-datepicker__navigation--next {
    right: 10px;
  }
  
  .react-datepicker__navigation:focus {
    outline: none;
  }
  
  .react-datepicker__navigation:hover {
    background-color: darkgreen;
  }
  
  .react-datepicker {
    transform: scale(1.2);
    transform-origin: top center;
  }
  
  .react-datepicker__day--in-selecting-range:not(.react-datepicker__day--selected) {
 
    background-color: rgba(9, 209, 9, 0.39);
    color: #dfd1d1;

  }
  

  .react-datepicker__day--in-selecting-range:not(.react-datepicker__day--selected):not(.react-datepicker__day--outside-month)  {
    
    background-color: rgba(0, 100, 0, 0.367);
    color: #FFF;
  }

  .react-datepicker__day--selected,
  .react-datepicker__day--in-range {
    background-color: rgba(0, 100, 0, 0.548);
    color: #555;
  }
  

  .react-datepicker__day--selected, .react-datepicker__day--in-range:not(.react-datepicker__day--outside-month) {
    background-color: green;
    color: #ffffff;
  }

  /* .react-datepicker__day:hover {
    background-color: rgba(0, 128, 0, 0.75);
  } */
  
  .react-datepicker-popper[data-placement^="bottom"] .react-datepicker__triangle {
    border-bottom-color: green;
  }
  
  .react-datepicker-popper[data-placement^="top"] .react-datepicker__triangle::before,
  .react-datepicker-popper[data-placement^="top"] .react-datepicker__triangle {
    border-top-color: green;
  }
  
  @media (max-width: 768px) {
    .react-datepicker {
      transform: scale(1.2);
    }
    .react-datepicker__day, .react-datepicker__day-name {
      font-size: 14px;
    }
  }
  
  .react-datepicker__day--disabled, .react-datepicker__month-text--disabled, .react-datepicker__quarter-text--disabled, .react-datepicker__year-text--disabled {
    cursor: not-allowed;
    color: #cccccc3e;
  }
  
  .react-datepicker__day--disabled, 
  .react-datepicker__month-text--disabled, 
  .react-datepicker__quarter-text--disabled, 
  .react-datepicker__year-text--disabled,
  .react-datepicker__day--disabled:hover, 
  .react-datepicker__month-text--disabled:hover, 
  .react-datepicker__quarter-text--disabled:hover, 
  .react-datepicker__year-text--disabled:hover {
    cursor: not-allowed;
    color: #cccccc1f;
    background: transparent;
  }


.react-datepicker__day--keyboard-selected {
  background-color: rgba(128, 0, 0, 0);
  color: #ffffff;
}

.react-datepicker__month-container {
  background-color: black;
}

