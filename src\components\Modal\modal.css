* {
    font-family: var(--font-main);
}

.custom-modal-overlay {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #0000009d;
    backdrop-filter: blur(6px);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
}

.botaoFecharModal {
    background-color: transparent;
    border: none;
    color: var(--primary-color);
    text-shadow: 1px 1px 1px #000;
    font-size: 1.5rem;
    cursor: pointer;
    position: relative;
}

.divBotaoFecharModal {
    display: flex;
    font-family: var(--font-main);
    justify-content: flex-end;
    margin-bottom: 3px;
}

.custom-modal {
    color: white;
    width: 50%;
    border: 2px solid var(--primary-color);
    background-color: rgba(0, 0, 0, 0.74);
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    z-index: 1001;
    margin: auto;
}


@media (max-width: 768px) {
    .custom-modal {
        width: 80%;
    }
}


@media (max-width: 480px) {
    .custom-modal {
        width: 95%;
        padding: 10px;
    }
}

button {
    cursor: pointer;
}

button:disabled {
    cursor: not-allowed;
}