// ---------------------- TIPAGEM E INTERFACES TERMINAL DE VENDAS ---------------------- //

export type Produto = {
  id: number;
  nome: string;
  valor: number;
};

export type OpcaoSelecao = {
  id: number;
  valor: number;
  label: string;
};

export interface ItemPedido {
  id?: string;
  descricao: string | null;
  quantidade: number;
  subtotal: number;
  desconto: number;
  produtoId: number | null;
  precoGenerico?: number | null;
}

export interface ImagemCapturada {
  dataUri: string;
}

export type OpcaoSelect = {
  id: number;
  label: string;
  valor: number;
  value: number;
};

export type OpcaoParcelas = {
  label: string;
  value: number;
};
export type OpcaoOrdem = {
  label: any;
  value: any;
};
export type OpcaoStatus = {
  label: any;
  value: any;
};
