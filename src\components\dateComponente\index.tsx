import { forwardRef } from "react";
import DatePicker, { registerLocale } from "react-datepicker";
import { FiCalendar } from "react-icons/fi";
import { ptBR } from "date-fns/locale";
import "react-datepicker/dist/react-datepicker.css";

import "./style.css";
import { CalendarDateInputProps, datasInterface } from "../../interfaces";

registerLocale("pt-BR", ptBR);

const CustomInput = forwardRef<HTMLButtonElement, CalendarDateInputProps>(({ value, onClick }, ref) => (
	<button className="custom-datepicker-input" onClick={onClick} ref={ref}>
		<p className="custom-datepicker-p">{value}</p> <FiCalendar className="calendarIconPicker" />
	</button>
));

function CalendarInput({ dataInicial, setDataInicial, dataFinal, setDataFinal, placeholder }: datasInterface) {
	return (
		<DatePicker
			dateFormat="dd  MMMM  yyyy"
			placeholderText={placeholder || "Selecione o período"}
			locale="pt-BR"
			onChange={(dates: [Date, Date]) => {
				const [initialDate, finalDate] = dates;
				setDataInicial(initialDate);
				setDataFinal(finalDate);
			}}
			selectsRange
			maxDate={new Date()}
			startDate={dataInicial}
			endDate={dataFinal}
			monthsShown={1}
			showDisabledMonthNavigation
			customInput={<CustomInput />}
		/>
	);
}

export default CalendarInput;
