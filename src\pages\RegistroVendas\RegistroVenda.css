#root {
  font-family: 'montserrat', sans-serif;
}

.inputData{
  background-color: var(--background-primary-color);
  border: 1px solid var(--primary-color);
  color: var(--second-color);
  border-radius: 5px;
  color-scheme: dark;
  width: min-content;
  text-align: center;
}
.placeholder{
  visibility: hidden;
  overflow: hidden;
}

#rowPedidos{
  justify-content: flex-start;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.motivoCancelamentoEstilo {

}

.motivoCancelamentoEstilo strong {
  color: gray;
}


.labelInputData{
  color: var(--second-color);
  margin: 0 0 5px 0;
}

.iconAtualizar{
  position: relative;
  top: 0;
}

.botaoPaginas:disabled {
  opacity: 0.4;
}
.registroVenda-container {
  margin-bottom: 40px;
  width: 100%;
  height: 100%;
  padding-left: 12px;
  padding-right: 12px;
}
.cardRegistroVenda{
  padding-bottom: 12px;
  height: min-content;
}
.cabecalhoFiltro {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.aprovadoCard {
    box-shadow: 10px 5px 5px var(--background-primary-color);
    color: var(--second-color);
    background-color: var(--background-primary-color);
    border: 1px solid var(--primary-color);
  }
  
  .aprovadoBotao {
    background-color: var(--status-aprovado-color);
    border-color: var(--status-aprovado-color);
  }

  .aprovadoBotao:disabled {
    background-color: var(--background-status-aprovado-color);
    border-color: var(--background-status-aprovado-color);
  }
  .aprovadoBotao:hover {
    background-color: var( --background-status-aprovado-color);
    border-color: var( --background-status-aprovado-color);
  }
  .aprovadoBotao:active {
    background-color: var( --background-status-aprovado-color) !important; 
    border-color: var( --background-status-aprovado-color) !important;
  }
  .aprovadoStatus {
    background-color: var(--background-status-aprovado-color);
    border-color: var(--status-aprovado-color);
  }

  

.pendenteCard {
    box-shadow: 10px 5px 5px var(--background-primary-color);
    color: var(--second-color);
    background-color: var(--background-primary-color);
    border: 1px solid var(--status-pendente-color);
  }
  .pendenteBotao {
    background-color: var(--status-pendente-color);
    border-color: var(--status-pendente-color);
  }

  .pendenteBotao:hover {
    background-color: var(--background-status-pendente-color);
    border-color: var(--background-status-pendente-color);
  }
  .pendenteBotao:active{
    background-color: var(--background-status-pendente-color) !important;
    border-color: var(--background-status-pendente-color) !important;
  }
  .pendenteStatus {
    background-color: var(--background-status-pendente-color);
    border-color: var(--status-pendente-color);
  }
  

  .canceladoCard {
    box-shadow: 10px 5px 5px var(--background-primary-color);
    color: var(--second-color);
    background-color: var(--background-primary-color);
    border: 1px solid var(--status-cancelado-color);
  }
  
  .canceladoBotao {
    background-color: var(--status-cancelado-color);
    border-color: var(--status-cancelado-color);
  }

  .canceladoBotao:hover {
    background-color: var(--background-status-cancelado-color);
    border-color: var(--background-status-cancelado-color);
  }
  .canceladoBotao:active{
    background-color: var(--background-status-cancelado-color) !important;
    border-color: var(--background-status-cancelado-color) !important;
  }
  .canceladoStatus {
    background-color: var(--background-status-cancelado-color);
    border-color: var(--status-cancelado-color);
  }


  .canceladoBotao:disabled {
    background-color: var(--background-status-cancelado-color);
    border: 1px solid var(--status-cancelado-color);
  }
  
  .meu-modal-conteudo {
    display: flex;
    flex-direction: column-reverse;
    justify-content: center;
    align-items: flex-end;
    flex-wrap: wrap;
    align-content: space-around;
  }

.meu-modal-conteudo img {
  max-width: 50vh;
  max-height: 70vh;
}


.emptyCard { 
  border: 1px solid #529E47;
  color: var(--second-color);
  display: flex;
  align-items: center;
  justify-content: center;
  height: 250px; 
  background-color: transparent; 
}

.emptyCardBody {
  text-align: center;
  color: var(--second-color); 
}

.emptyIcon {
  font-size: 48px; 
  margin-bottom: 16px; 
  color: var(--second-color); 
}

.statusCard{
  display: flex;
  width: 100%;
  padding: 8px;
  flex-direction: row;
  justify-content: space-between;
}
h4{
  color: var(--second-color);
  margin: 20px 20px 20px 30px;
}

.botaoConfirmarPedido:hover{
  background-color: var(--background-status-aprovado-color);
  border-color: var(--status-aprovado-color);
}
.botaoConfirmarPedido:active{
  background-color: var(--primary-color-down-variant) !important;
  border-color: var(--primary-color-down-variant) !important;
}
.botaoImagemComprovante{
  background-color: var(--status-imagem-color);
  border-color: var(--status-imagem-color) ;
  margin-left: 5px;
}
.botaoImagemComprovante:hover{
  background-color: var(--background-status-imagem-color);
}
.botaoImagemComprovante:active{
  background-color: var(--background-status-imagem-color) !important;
}
.iconPDF{
  width: 23px;
  height: 23px;
}

.botaoImagem {
 margin-left: 5px;
}

.botaoSincronizar {
  background-color: var(--primary-color);
  border: var(--primary-color);
  width: 40px;
  display: flex; 
  justify-content: center;
  align-items: center;
  height: 40px;
  margin: 0 10px 0 0;
  border-radius: 5px;
}


::-webkit-scrollbar {
  width: 8px; 
}

::-webkit-scrollbar-thumb {
  background-color: var(--primary-color); 
  border-radius: 4px; 
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--primary-color-down-variant); 
}


::-webkit-scrollbar-track {
  background-color: transparent; 
}

/* Estilo global ou em um arquivo CSS */
@media only screen and (max-width: 768px) {
  .meu-modal {
    max-width: 100%;
    max-height: 100%;
  }
  .meu-modal-cabecalho {
    font-size: 16px;
  }
  .meu-modal-corpo {
    padding: 10px;
    max-width: 100%;
    /* max-height: 60vh; */
  }
  .meu-modal-conteudo {
    /* max-height: 70vh; */
    max-width: 100%;
  }
  .divBotaoSincronizar{
    width: 14% !important;
  }
  .divFiltro{
    width: 28% !important;
  }




}

.comprovanteBotao{
  background-color: var(--status-pendente-color);
  border:  1px solid var(--status-pendente-color);
  margin-left: 10px;
}
.comprovanteBotao:hover{
  background-color: var(--background-status-pendente-color);
  border-color: var(--status-pendente-color);
}
.comprovanteBotao:active{
  background-color: var(--background-status-pendente-color) !important;
  border-color: var(--status-pendente-color) !important;
}
.imageIcone{
  width: 23px;
  height: 23px;
}
.inputFiltros{
  border-radius: 5px;
}
.filtros{
  
  margin: 0 7px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 20px;
}
.divConteudoRegistroVendas{
  min-height: calc(100vh - 150px);
  
  align-items: flex-start;
} 


.divFiltro{
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
  width: 20%;
  justify-content: flex-end;
}
.divBotaoSincronizar{
  display: flex;
  align-items: flex-end;
  width: 35%;
  justify-content: flex-end;
}
.paginacao{
  display: flex;
  flex-direction: row;
  justify-content: center;
  margin-top: auto;
}
.botaoPaginas{
  background-color: transparent;
  color: white;
  border:none
}
.iconPagina{
  height: 30px;
  width: 30px;
}
.paginaDiv{
  color: white;
  display: flex;
  align-items: center;
}

.voltarBotao {
  background-color: #565e64;
  border: none;
  width: 50px;
  height: 40px;
  margin: 10px 0 0 0;
  border-radius: 5px;
}
.voltarBotao:hover{
  background-color: #0f1011;
}
.voltarBotao:active{
  background-color: #565e64;
}

.voltarBotao .iconeBotaoVoltar {
  width: 23px;
  height: 24px;
}

.campo-motivo-cancelamento {
  border: 1px solid var(--primary-color);
  background-color: #0f1011;

  color: var(--second-color);
  border-radius: 5px;
  color-scheme: dark;
  width: 100%;
  text-align: start;
  padding: 10px;
  resize: none;
  height: 100px;
  overflow: auto;
  margin-bottom: 10px;
}

.campo-motivo-cancelamento:focus {
  border: 1px solid var(--primary-color);
}

.campo-motivo-cancelamento:active {
  border: 1px solid var(--primary-color);
}


.col {
  margin-bottom: 10px;
}

.corpo-card-registro {
  width: 100%;
  border-radius: 5px;
}

.divDataRegistroVendas {
  padding: 10px;
}

.divBotoesRegistroVendas {
  display: flex;
  flex-direction: row;
  justify-content: start;
  align-items: center;
  margin-top: 10px;
}

.botaoRegistroVendas {
  border: none;
  display: flex;
  width: 50px;
  height: 40px;
  margin: 0 10px 0 0;
  border-radius: 5px;
  justify-content: center;
  align-content: center;
  flex-direction: row;
  flex-wrap: wrap;
}

.nomeCliente {
  font-size: 18px;
  font-weight: bold;
}

.nomeCliente strong {
  font-weight: lighter;
  font-size: 16px;
  color: rgb(141, 137, 137);
}

.divPedidos {
  width: 100%;
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
}

.card-corpo-registro-vendas {
  padding: 5px;
  width: 100%;
  height: 100%;
}
.flex-equal-height > [class*='mb-4']  {
    display: flex;
    height: 100%;
    flex-direction: column;
    flex-wrap: wrap;
    align-content: center;
}

.flex-equal-height > [class*='mb-4'] > .card {
  flex: 1;
}

.motivoCancelamento {
  background-color: transparent;
  border: none;
  text-decoration: none;
  cursor: pointer;
  color: white;
  text-align: justify;
}
select{
  appearance: none;
  -webkit-appearance: none;
}

.selectFiltro{
  width: 100%;
  color: black;
}

.sublinhado {
  text-decoration: underline;
  font-weight: none;
}

.semImagem {
  height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  flex-wrap: wrap;
  align-content: center;
}

.iconeSemImagem {
  width: 100px;
  height: 100px;
}

.semImagem p {
  color: var(--second-color);
  font-size: 20px;
  font-weight: bold;
}

.maximoCaracteresMotivo {
  color: red;
  font-size: 14px;
  top: 0;
  text-align: left;
}

.labelRegistroVendas {
  width: 100%;
}

.divBaixarRelatorioPedido {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  margin-top: 10px;
}

.botaoBaixarRelatorioPedidos {
  background-color: var(--primary-color);
  border: var(--primary-color);
  width: 50px;
  height: 50px;
  margin: 0 10px 0 0;
  border-radius: 5px;
}

.pdfLink {
  color: var(--second-color);
  text-decoration: none;
}

.iconeBaixarRelatorioPedidos {
  width: 30px;
  height: 30px;
  padding: 5px;
}

.pdfViewer {
  width: 100%;
  height: 100%;
  border: none;
}

.divVisualizarRelatorioPedido {
  width: 100%;
  height: 60vh;
  display: flex;
  flex-direction: column;
}

.botaoAbrirRelatorioPedidos {
  cursor: pointer;
  background-color: var(--primary-color);
  border: var(--primary-color);
  width: 50px;
  height: 50px;
  margin: 0 10px 0 0;
  border-radius: 5px;
}

.card-registro-vendas {
  padding: 10px;
  width: 33.33333%;
}

.texto-card-registro-vendas {
 width: 100%;
 font-size: 16px;
 text-align: justify;
 font-family: var(--font-main);
 font-weight: bold;
 color: var(--second-color);
 margin-top: 5px;
 display: flex;
 flex-wrap: wrap;
 align-content: flex-start;

}

.texto-card-registro-vendas strong {
  font-weight: lighter;
  color: rgb(187, 179, 179);
  padding-left: 5px;
}
@media only screen and (max-width: 992px) {
  .divBaixarRelatorioPedido {
    justify-content: center;
  }

  .card-registro-vendas {
    width: 50%;
  }
}

@media only screen and (max-width: 768px) {
  .divBaixarRelatorioPedido {
    justify-content: center;
  }


}

@media only screen and (max-width: 576px) {
  .divBaixarRelatorioPedido {
    justify-content: center;
  }

  .card-registro-vendas {
    width: 100%;
  }
}

.divModalRelatorio {
      width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column-reverse;
        flex-wrap: wrap;
        align-content: flex-start;
}

.tituloModalRelatorio {
  font-size: 20px;
  font-weight: bold;
  color: var(--second-color);
  margin-bottom: 10px;
}

.divModalConfirmarPedido {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  padding: 1;
}

.botaoComprovanteModal {
  background-color: var(--status-pendente-color);
  border: var(--status-pendente-color);
  width: 50px;
  height: 40px;
  margin: 10px 10px 10px 0;
  border-radius: 5px;
}

.botaoModalAprovarPedido {
  width: 100px;
  height: 40px;
  color: white;
  border: none;
  margin: 10px 10px 10px 0;
  border-radius: 5px;
}

.divBotoesAprovarPedido {
  display: flex;
  flex-direction: row;  
  color: white;
  justify-content: flex-start ;
  align-items: center;
  margin-top: 10px;
}

.divModalConfirmarPedidoMain {
  width: 100%;
  height: 100%;
  display: flex;
  font-family: var(--font-main);
  justify-content: center;
  flex-direction: column;
  flex-wrap: wrap;
  padding: 1;
}

.TituloModalConfirmarPedido {
  font-weight: bold;
  color: var(--second-color);
  margin-bottom: 10px;
}

.textoModalConfirmarPedido {
  color: var(--second-color);
  margin-bottom: 10px;
}

.divBotoesECampoMotivo {
  font-family: var(--font-main);
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.tituloModalImagem {
  font-weight: bold;
  font-family: var(--font-main);
  color: var(--second-color);
  margin-bottom: 10px;
}

.textoSemImagemModal {
  color: var(--second-color);
  margin-bottom: 10px;
  font-family: var(--font-main);
}


.botaoConfirmarPedido{
  width: 70px;
  border-color: var(--primary-color);
  background-color: var(--primary-color);
  color: var(--second-color);

}

.cardVazio {
  opacity: 0;
}