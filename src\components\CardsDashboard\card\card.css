* {
    font-family: var(--font-main);
}

.cardDashboard {
    color: var(--second-color);
    width: auto;
    position: relative;
    display: flex;
    flex-direction: column;
    border-radius: 4px;
    height: 100%;
    border: 1px solid var(--primary-color);
    transition: transform .2s ease-in-out;
    flex-wrap: nowrap;
}

.corpoCard {
    display: flex; 
    flex-direction: column;
    justify-content: center;
    width: 100%;
    background-color: black;
    height: 100%;
}

.cabecalhoCard {
    background-color: var(--primary-color);
    border-radius: 2px 2px 0 0;
    padding: 5px;
}

.cabecalhoCard h1 {
    margin: 0;
    font-size: 20px;
    color: white;
}

.cardDashboard:hover {
    transform: scale(.95);

}

.ulCardDashboard {
    list-style-type: none;
    text-decoration: none;
    display: flex;
    justify-content: center;
    width: 100%;
    height: 100%;
    flex-direction: column;
    padding: 0;
}

.liCardDashboard {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    height: 100%;
    border-bottom: 1px solid var(--primary-color);
}

.divCardDashboard {
    display: flex;
    width: 100%;
    flex-direction: column;
    background-color: transparent;
    height: 100%;
}

.spanCardNumero {
    background-color: rgba(0, 128, 0, 0.356);
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    /* width: 20px; */
    padding: 2px;
    height: 25px;
    border-radius: 6px;
}

.spanCardMonetario {
    background-color: rgba(0, 128, 0, 0.356);
    width: auto;
    color: white;
    display: flex;
    margin-right: 2px;
    justify-content: center;
    align-items: center;
    padding: 3px;
    border-radius: 6px;
}

.divCardVendas {
    display: flex;
    justify-content: space-between;
    align-items: center;

}

.divCardVazio {
    display: flex;
    justify-content: center;
    height: 50px;
    align-items: center;
}

.liCardVazio {
    text-decoration: none;
    list-style-type: none;
}