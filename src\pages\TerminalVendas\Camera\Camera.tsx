import { useState, useRef } from "react";
import { BsCamera } from "react-icons/bs";
import { AiOutlineRotateLeft, AiOutlineClose } from "react-icons/ai";
import Webcam from "react-webcam";
import "./Camera.css";
import { toast } from "react-toastify";

interface ImageCaptureComponentProps {
  onCapture: (image: string) => void;
}

function ImageCaptureComponent({ onCapture }: ImageCaptureComponentProps) {
  const [mostrarCamera, setMostrarCamera] = useState(false);

  const webcamRef = useRef<Webcam | null>(null);
  const [trocarCamera, setTrocarCamera] = useState<"user" | "environment">(
    "environment"
  );

  const manipularCapturaClickBotao = () => {
    setMostrarCamera(true);
  };

  const manipularCapturaImagem = () => {
    if (webcamRef.current) {
      const screenshot = webcamRef.current.getScreenshot();
      if (screenshot) {
        onCapture(screenshot);
        // setImage(screenshot);
         toast.success("Foto salva");
        setMostrarCamera(false);
      
      }
    }
  };

  const manipularFecharCamera = () => {
    setMostrarCamera(false);
  };



  if (!mostrarCamera) {
    return (
      <button className="imagemButton" onClick={(e) => {
        e.preventDefault();
        manipularCapturaClickBotao();
      }}>
        <BsCamera className="imageIcon" />
      </button>
    );
  }



  return (
    <>
      <div className="cameraContainer">
        <Webcam
          screenshotQuality={2}
          ref={webcamRef}
          style={{ width: "100%", height: "100%" }}
          screenshotFormat="image/jpeg"
          videoConstraints={{
            facingMode: trocarCamera,
          }}
        />

        <div className="cameraButtonContainer">
          <button className="cameraButton" onClick={(e) => {
            e.preventDefault();
            manipularCapturaImagem();
          }}>
            <BsCamera size={32} />
          </button>
        </div>
        <div className="cameraCloseButton" onClick={(e) => {
          e.preventDefault();
          manipularFecharCamera();
        }}>
          <AiOutlineClose size={24} />
        </div>

        
        <button
          className="toggleCameraButton"
          onClick={(e) => {
            e.preventDefault();
            setTrocarCamera((prevMode) =>
              prevMode === "environment" ? "user" : "environment"
            );
          }}
        >
          <AiOutlineRotateLeft size={24} />
        </button>
      </div>

      {/* <Toast
        onClose={() => setMostrartoast(false)}
        show={mostrarToast}
        delay={2000}
        autohide
      >
        <Toast.Body>Foto salva</Toast.Body>
      </Toast> */}


      <div className="divcameraBotao">
        <h1>Clique na imagem</h1>
      <button className="imagemButton" onClick={(e) => {
        e.preventDefault();
        manipularCapturaClickBotao();
      }}>
        <BsCamera className="imageIcon" />
      </button>
      </div>
    </>
  );
}

export default ImageCaptureComponent;
