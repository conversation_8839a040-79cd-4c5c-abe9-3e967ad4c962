import { useEffect } from "react";

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FaReg<PERSON>lock, FaTimes } from "react-icons/fa";
import { BsCardImage } from "react-icons/bs";
import { IoDocumentTextOutline, IoReload } from "react-icons/io5";
import "./RegistroVenda.css";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

import { AiOutlineArrowLeft } from "react-icons/ai";
import { MdOutlineImageNotSupported } from "react-icons/md";

import { CiCreditCardOff } from "react-icons/ci";
import {
	MdOutlineKeyboardArrowRight,
	MdOutlineKeyboardArrowLeft,
	MdOutlineKeyboardDoubleArrowRight,
	MdOutlineKeyboardDoubleArrowLeft,
} from "react-icons/md";
import ReactPDF from "@react-pdf/renderer";

import { ComponenteProtegido, UsuarioRoles } from "../../contexts/autenticacao/AuthContext";
import Select from "react-select";
import ComponenteCarregamento from "../../contexts/carregamento/carregamento";
import { MdDownload, MdError } from "react-icons/md";
import { AiOutlineLoading3Quarters } from "react-icons/ai";
import { FaRegEye } from "react-icons/fa";
import AvisoNaoSuportaPDF, { verificarSeNavegadorSuportaPDF } from "../../components/AvisoPDF";
import useRegistroVendas from "../../hooks/useRegistroVendas";
import ModalFRP from "../../components/Modal";
import { PedidoRegistro } from "../../interfaces";
import GeradorDePDF from "../../components/Relatorios/ReactPdf/relatorioVenda";
import CalendarInput from "../../components/dateComponente";

const NovoRegistro: React.FC = () => {
	const {
		pedidos,
		status,
		setStatus,
		quantidadeFiltro,
		setQuantidade,
		ordenar,
		setOrdenar,
		recarregar,
		urlImagem,
		setUrlImagem,
		pdfPedidoId,
		confirmModalShow,
		setConfirmModalShow,
		modalShow,
		setModalShow,
		confirmPedidoId,
		setConfirmPedidoId,
		modalImagemShow,
		setModalImagemShow,
		paginaAtual,
		setPaginaAtual,
		motivoCancelamento,
		setMotivoCancelamento,
		mostrarCampoMotivo,
		setMostrarCampoMotivo,
		carregamento,
		opcaoSelecionada,
		setOpcaoSelecionada,
		exibirMotivoAberto,
		manipularConfirmacao,
		buscarImagem,
		sincronizar,
		BuscarPedidosFiltrados,
		modificarPagina,
		abrirExibirMotivo,
		handleAbrirPDF,
		identificarIframeEAbrirPDF,
		ultimaPagina,
		dataInicial,
		setDataInicial,
		dataFinal,
		setDataFinal,
	} = useRegistroVendas();

	const renderPedidos = (pedidosArray: PedidoRegistro | undefined) => {
		if (pedidosArray !== undefined)
			return pedidosArray.pedidos.map((pedido, indice) => (
				<div className={`card-registro-vendas ${pedido.status === "falso" ? "cardVazio" : ""} flex-equal-height cardRegistroVenda`}>
					<div
						className={`mb-4 corpo-card-registro ${
							pedido.status === "Aprovado" ? "aprovadoCard" : pedido.status === "Pendente" ? "pendenteCard" : "canceladoCard"
						}`}
					>
						<div className="card-corpo-registro-vendas">
							<div className="divDataRegistroVendas">
								<h1 className="nomeCliente">
									Cliente: <strong>{pedido.cliente || "Não informado"}</strong>
								</h1>
								<div>
									<p className="texto-card-registro-vendas">
										ID: <strong>{pedido.id}</strong>
									</p>
									<p className="texto-card-registro-vendas">
										Total: <strong> R$ {pedido.total}</strong>
									</p>
									<p className="texto-card-registro-vendas">
										Pagamento: <strong>{pedido.pagamento}</strong>
									</p>
									<p className="texto-card-registro-vendas">
										Vendedor: <strong>{pedido.vendedor}</strong>
									</p>
									<p className="texto-card-registro-vendas">
										{/* <strong>Criado em:</strong> {new Date(pedido.criadoEm).toLocaleDateString()} */}
										Criado em: <strong>{new Date(pedido.criadoEm).toLocaleDateString()}</strong>
									</p>
									{pedido.status === "Cancelado" && pedido.cancelamento && pedido.cancelamento.split(" ").length > 5 ? (
										<div>
											<a
												className="motivoCancelamento"
												href="#"
												onClick={
													exibirMotivoAberto === indice ? () => abrirExibirMotivo(indice) : () => abrirExibirMotivo(indice)
												}
											>
												{exibirMotivoAberto !== indice ? (
													<p className="dentro">
														Motivo:{" "}
														<span className="sublinhado">
															{pedido.cancelamento
																?.split(" ")
																.slice(0, 3)
																.map(palavra => (palavra.length > 10 ? palavra.slice(0, 10) + "..." : palavra))
																.map(palavra => (palavra.endsWith("...") ? palavra.slice(0, -3) : palavra))
																.join(" ") + "..."}{" "}
														</span>
													</p>
												) : (
													<p className="dentro motivoCancelamentoEstilo">
														Motivo: <strong>{pedido.cancelamento}</strong>
													</p>
												)}
											</a>
										</div>
									) : pedido.status === "Cancelado" && pedido.cancelamento ? (
										<p className="motivoCancelamentoEstilo">
											Motivo: <strong>{pedido.cancelamento}</strong>
										</p>
									) : null}
								</div>
								<div className="divBotoesRegistroVendas">
									<button
										className={`botaoRegistroVendas ${
											pedido.status === "Aprovado"
												? "aprovadoBotao"
												: pedido.status === "Pendente"
												? "pendenteBotao"
												: "canceladoBotao"
										}`}
										onClick={() => handleAbrirPDF(pedido.id)}
									>
										<IoDocumentTextOutline className="iconPDF" />
									</button>
									<ComponenteProtegido rolesNecessarias={[UsuarioRoles.Admin, UsuarioRoles.Financeiro]} fallback>
										{pedido.status === "Pendente" && (
											<button
												className="botaoRegistroVendas botaoConfirmarPedido"
												onClick={() => {
													setConfirmPedidoId(pedido.id);
													setConfirmModalShow(true);
												}}
											>
												Avaliar
											</button>
										)}
									</ComponenteProtegido>
									<button className="botaoRegistroVendas botaoImagemComprovante" onClick={() => buscarImagem(pedido.id)}>
										<BsCardImage className="imageIcone" />
									</button>
								</div>
							</div>
						</div>

						<div
							className={`statusCard ${
								pedido.status === "Aprovado" ? "aprovadoStatus" : pedido.status === "Pendente" ? "pendenteStatus" : "canceladoStatus"
							}`}
						>
							<div>Status: {pedido.status}</div>

							<div>
								{pedido.status === "Aprovado" ? (
									<FaCheck />
								) : pedido.status === "Pendente" ? (
									<FaRegClock />
								) : pedido.status === "Cancelado" ? (
									<FaTimes />
								) : null}
							</div>
						</div>
					</div>
				</div>
			));
	};

	useEffect(() => {
		BuscarPedidosFiltrados();
	}, [BuscarPedidosFiltrados, recarregar, paginaAtual, ordenar, quantidadeFiltro, status, dataInicial, dataFinal]);

	useEffect(() => {
		BuscarPedidosFiltrados();
		return setPaginaAtual(1);
	}, [quantidadeFiltro, status, ordenar, BuscarPedidosFiltrados, setPaginaAtual]);

	const nenhumPedido = () => {
		return (
			<div className="nenhumaExpedicao">
				<div className="divNenhumaExpedicao">
					<CiCreditCardOff className="iconeNenhumaExpedicao" />
					<h2>Nenhum pedido disponível.</h2>
				</div>
			</div>
		);
	};

	return (
		<>
			<div className="registroVenda-container">
				<ComponenteCarregamento carregando={carregamento}>
					<div className="divConteudoRegistroVendas">
						<div>
							<h2>Vendas</h2>
							<div className="filtros">
								<div className="divFiltro">
									<label className="labelRegistroVendas" id="statusRegistro">
										Status:
										<div className="selectFiltro">
											<Select
												aria-labelledby="statusRegistro"
												isSearchable={false}
												styles={{
													option: provided => ({
														...provided,
														color: "black",
														cursor: "pointer",
													}),
													control: styles => ({
														...styles,
														cursor: "pointer",
													}),
												}}
												theme={theme => ({
													...theme,
													colors: {
														...theme.colors,
														primary25: "#009f311c",
														primary: "#009f3c",
													},
												})}
												defaultValue={{
													value: "Pendente",
													label: "Pendente",
												}}
												options={[
													{
														value: "Pendente",
														label: "Pendente",
													},
													{
														value: "Aprovado",
														label: "Aprovado",
													},
													{
														value: "Cancelado",
														label: "Cancelado",
													},
												]}
												onChange={option => {
													setStatus(
														option
															? (
																	option as {
																		value: string;
																		label: string;
																	}
															  ).value
															: "Pendente"
													);
												}}
											/>
										</div>
									</label>
								</div>
								<div className="divFiltro">
									<label className="labelRegistroVendas" id="quantidadeRegistro">
										Quantidade:
										<div className="selectFiltro">
											<Select
												aria-labelledby="quantidadeRegistro"
												styles={{
													option: provided => ({
														...provided,
														color: "black",
														cursor: "pointer",
													}),
													control: styles => ({
														...styles,
														cursor: "pointer",
													}),
												}}
												isSearchable={false}
												theme={theme => ({
													...theme,
													colors: {
														...theme.colors,
														primary25: "#009f311c",
														primary: "#009f3c",
													},
												})}
												defaultValue={
													opcaoSelecionada
														? opcaoSelecionada
														: {
																value: 10,
																label: "10",
														  }
												}
												options={[
													{ value: 10, label: "10" },
													{ value: 20, label: "20" },
													{ value: 30, label: "30" },
													{ value: 50, label: "50" },
												]}
												onChange={option => {
													setQuantidade(option ? Number(option.value) : 10);

													setOpcaoSelecionada(
														option
															? option
															: {
																	value: 10,
																	label: "10",
															  }
													);
												}}
											/>
										</div>
									</label>
								</div>
								<div className="divFiltro">
									<label className="labelRegistroVendas" id="ordenarRegistro">
										Ordenar por:
										<div className="selectFiltro">
											<Select
												aria-labelledby="ordenarRegistro"
												styles={{
													option: provided => ({
														...provided,
														color: "black",
														cursor: "pointer",
													}),
													control: styles => ({
														...styles,
														cursor: "pointer",
													}),
												}}
												isSearchable={false}
												theme={theme => ({
													...theme,
													colors: {
														...theme.colors,
														primary25: "#009f311c",
														primary: "#009f3c",
													},
												})}
												defaultValue={{
													value: "DESC",
													label: "Mais recentes",
												}}
												options={[
													{
														value: "ASC",
														label: "Mais antigos",
													},
													{
														value: "DESC",
														label: "Mais recentes",
													},
												]}
												onChange={option => setOrdenar(option ? option.value : "ASC")}
											/>
										</div>
									</label>
								</div>

								<div className="divFiltro">
									<label htmlFor="" className="labelDashboard">
										Periodo de tempo:
									</label>

									<CalendarInput
										dataFinal={dataFinal || undefined}
										dataInicial={dataInicial || undefined}
										setDataFinal={setDataFinal}
										setDataInicial={setDataInicial}
									/>
								</div>
								<div className="divBotaoSincronizar">
									<button className="botaoSincronizar" onClick={sincronizar}>
										<IoReload className="iconPDF iconAtualizar" />
									</button>
								</div>
							</div>
						</div>

						<div className="divPedidos">
							<div id="rowPedidos">{pedidos?.pedidos.length === Number(0) ? nenhumPedido() : renderPedidos(pedidos)}</div>
						</div>
					</div>
					<div className="divCardPedidos">
						<div className="paginacao">
							<button className="botaoPaginas" onClick={() => modificarPagina(1)} disabled={paginaAtual === 1}>
								<MdOutlineKeyboardDoubleArrowLeft className="iconPagina" />
							</button>
							<button className="botaoPaginas" onClick={() => modificarPagina(paginaAtual - 1)} disabled={paginaAtual === 1}>
								<MdOutlineKeyboardArrowLeft className="iconPagina" />
							</button>
							<div className="paginaDiv">
								Página {paginaAtual} de {ultimaPagina}
							</div>
							<button className="botaoPaginas" onClick={() => modificarPagina(paginaAtual + 1)} disabled={paginaAtual === ultimaPagina}>
								<MdOutlineKeyboardArrowRight className="iconPagina" />
							</button>
							<button className="botaoPaginas" onClick={() => modificarPagina(ultimaPagina)} disabled={paginaAtual === ultimaPagina}>
								<MdOutlineKeyboardDoubleArrowRight className="iconPagina" />
							</button>
						</div>
					</div>
				</ComponenteCarregamento>
			</div>

			<ModalFRP estaAberto={modalShow} aoSolicitarFechar={() => setModalShow(false)} style={{ zIndex: 1050 }}>
				<h1 className="titulo">Comprovante de Venda</h1>

				<div className="divModalRelatorio">
					<div className="divBaixarRelatorioPedido">
						<button className="botaoBaixarRelatorioPedidos">
							<ReactPDF.PDFDownloadLink
								document={<GeradorDePDF id={pdfPedidoId || 0} />}
								fileName="comprovante.pdf"
								className="pdfLink"
							>
								{({ blob, loading, error }) => {
									if (loading) return <AiOutlineLoading3Quarters className="iconeBaixarRelatorioPedidos" />;
									if (error) return <MdError className="iconeBaixarRelatorioPedidos" />;
									if (blob) return <MdDownload className="iconeBaixarRelatorioPedidos" />;
									else return <p>Download</p>;
								}}
							</ReactPDF.PDFDownloadLink>
						</button>
						{verificarSeNavegadorSuportaPDF() ? (
							<button className="botaoAbrirRelatorioPedidos" onClick={identificarIframeEAbrirPDF}>
								<FaRegEye className="iconeAbrirRelatorioPedidos" />
							</button>
						) : (
							<></>
						)}
					</div>
					<div className="divVisualizarRelatorioPedido">
						{verificarSeNavegadorSuportaPDF() ? (
							<ReactPDF.PDFViewer className="pdfViewer">{<GeradorDePDF id={pdfPedidoId || 0} />}</ReactPDF.PDFViewer>
						) : (
							<AvisoNaoSuportaPDF />
						)}
					</div>
					<div></div>
				</div>
			</ModalFRP>

			<ModalFRP
				estaAberto={confirmModalShow}
				aoSolicitarFechar={() => {
					setConfirmModalShow(false);
					setMostrarCampoMotivo(false);
				}}
			>
				<div className="divModalConfirmarPedidoMain">
					<h1 className="TituloModalConfirmarPedido">Confirmar Pedido</h1>
					<div className="divModalConfirmarPedido">
						<p className="textoModalConfirmarPedido">Você deseja confirmar a venda?</p>
						<p className="textoModalConfirmarPedido">Verifique se as informações estão corretas</p>
						<button
							className={`botaoComprovanteModal comprovanteBotao pendenteBotao`}
							onClick={() => handleAbrirPDF(confirmPedidoId || 0)}
						>
							<IoDocumentTextOutline className="iconPDF" />
						</button>
						{mostrarCampoMotivo && (
							<>
								<textarea
									className="campo-motivo-cancelamento"
									placeholder="Motivo do cancelamento"
									onChange={e => {
										setMotivoCancelamento(e.target.value);
									}}
									maxLength={255}
								/>
								{motivoCancelamento.length > 254 ? (
									<p className="maximoCaracteresMotivo">Limite máximo de caracteres atingido</p>
								) : null}
							</>
						)}
						<div className="divBotoesECampoMotivo">
							{mostrarCampoMotivo && (
								<button
									className="voltarBotao"
									onClick={() => {
										setMostrarCampoMotivo(false);
										setMotivoCancelamento("");
									}}
								>
									<AiOutlineArrowLeft className="iconeBotaoVoltar" />
								</button>
							)}
							<div className="divBotoesAprovarPedido">
								<button
									className="aprovadoBotao botaoModalAprovarPedido"
									onClick={() => manipularConfirmacao(confirmPedidoId || 0, "Aprovado")}
									disabled={mostrarCampoMotivo === true}
								>
									Aprovar pedido
								</button>
								<button
									className="canceladoBotao botaoModalAprovarPedido"
									disabled={mostrarCampoMotivo === true && motivoCancelamento.length < 5}
									onClick={() => {
										if (mostrarCampoMotivo === false) {
											setMostrarCampoMotivo(true);
											return;
										}
										if (motivoCancelamento.length < 5) {
											toast.error("Motivo do cancelamento deve conter pelo menos 5 caracteres", {
												toastId: "motivoCancelamento",
											});
											return;
										}
										if (motivoCancelamento.length > 256) {
											toast.error("Motivo do cancelamento deve conter no máximo 256 caracteres", {
												toastId: "motivoCancelamentoMaximo",
											});
											return;
										}
										manipularConfirmacao(confirmPedidoId || 0, "Cancelado");
									}}
								>
									Cancelar pedido
								</button>
							</div>
						</div>
					</div>
				</div>
			</ModalFRP>

			<ModalFRP
				estaAberto={modalImagemShow}
				aoSolicitarFechar={() => {
					setModalImagemShow(false);
					setUrlImagem(null);
				}}
				style={{ zIndex: 1050 }}
			>
				<h1 className="tituloModalImagem">Imagem do pedido</h1>
				<div>
					{urlImagem ? (
						<img
							src={urlImagem}
							alt="Imagem do Pedido"
							style={{ width: "100%", height: "auto", maxHeight: "500px", objectFit: "contain" }}
						/>
					) : (
						<div className="semImagem">
							<MdOutlineImageNotSupported className="iconeSemImagem" />
							<p className="textoSemImagemModal">Sem Imagem</p>
						</div>
					)}
				</div>
			</ModalFRP>
		</>
	);
};

export default NovoRegistro;
