import { AxiosError } from "axios";
import api from "../apiService";
import Cookies from "js-cookie";
import { useEffect, useState } from "react";
import { Usuario } from "../interfaces";

const useListaUsuarios = () => {
    const [usuarios, setUsuarios] = useState<Usuario[]>([]);
    const [error, setError] = useState<string | null>(null);
    const [carregando, setCarregando] = useState(true);
  
    useEffect(() => {
        buscarUsuarios();
    }, []);
  
    const buscarUsuarios = async (): Promise<void> => {
      const token = Cookies.get("token");
  
      try {
        const response = await api.get(`/usuario`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });
        setUsuarios(response.data);
  
        setCarregando(false);
      } catch (err) {
        if ((err as AxiosError).response) {
          setError(
            (err as { response: { status: number } }).response.status === 401
              ? "Não autorizado"
              : "Erro ao buscar usuários"
          );
        } else {
          setError("Não foi possível conectar aos usuários");
        }
        setCarregando(false);
      }
    };
  

const formatarNomes = (nome: string) => {
    return nome
      .toLowerCase()
      .split(" ")
      .map((s) => s.charAt(0).toUpperCase() + s.substring(1))
      .join(" ");
  };


    return { buscarUsuarios, usuarios, error, carregando, formatarNomes };
  
}

export default useListaUsuarios