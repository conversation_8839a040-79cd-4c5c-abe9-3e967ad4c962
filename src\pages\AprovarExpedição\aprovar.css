.aprovar-container {
    display: flow-root;
    padding-left: 12px;
    padding-right: 12px;
    height: 100%;
}

.divHeaderExpedicoes {
    min-height: calc(100vh - 150px);
    align-items: flex-start;
}

.expedicao-div {
    background-color: black;
    display: grid;
    /* background-color: var(--background-second-color); */
    padding: 10px;
    border-radius: 10px;
    border: 1px solid black;
    color: #ffffff;
    font-size: 16px;
    margin-bottom: 15px;
}

#rowExpedicoes {
    transition: max-height 0.5s ease-in-out;
}

.divPaginacao {
    margin-top: 50px;
}

.bordaPendente {
    border-color: var(--status-pendente-color) !important;
}

.bordaCancelado {
    border-color: var(--status-cancelado-color) !important;
}

.bordaAprovado {
    border-color: var(--primary-color) !important;
}

.bordaCinza {
    border-color: #1c1c1c !important;
    color: gray;
}

.produto-div {
    background-color: var(--background-second-color);
    padding: 10px;
    border: 1px solid var(--primary-color);
    border-radius: 10px;
    margin-bottom: 10px;
    margin-top: 10px;
}


.numero-expedicao {
    font-size: 20px;
    font-weight: bold;
    margin-right: 10px;
    justify-self: end;
}

.numero-expedicao-strong {
    margin-left: 7px;
}

.botaoPaginas:disabled {
    opacity: 0.4;
}


.alterarStatusBotoes {
    display: flex;
    justify-content: space-between;
}

.alterarStatusBotoes button {
    margin-right: 10px;
    display: flex;
    border-radius: 10px;
    color: #ffffff;
    font-size: 16px;
    padding: 5px;
    cursor: pointer;
    flex-direction: row;
    flex-wrap: wrap;
    align-content: center;
    justify-content: center;
    align-items: center;
}

.titleProduto {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
}

.titleProduto strong {
    color: rgb(141, 137, 137);
}

.alterarStatusBotoes .botaoAprovar {
    background: var(--status-aprovado-color);
    border: 1px solid var(--status-aprovado-color);
}

.alterarStatusBotoes .botaoAprovar:hover {
    background-color: var(--background-status-aprovado-color);
}

.alterarStatusBotoes .botaoAprovar:active {
    background-color: var(--background-status-aprovado-color);
}

.alterarStatusBotoes .botaoRecusar {
    background: var(--status-cancelado-color);
    border: 1px solid var(--status-cancelado-color);
}

.alterarStatusBotoes .botaoRecusar:hover {
    background-color: var(--background-status-cancelado-color);
}

.alterarStatusBotoes .botaoRecusar:active {
    background-color: var(--background-status-cancelado-color);
}

.iconeBotaoAprovar,
.iconeBotaoRecusar {
    width: 23px;
    height: 23px;
    margin-left: 5px;
    margin-bottom: 2px;
}

.menuAprovarExpedicao {
    display: flex;
    justify-content: space-between;
    font-size: 20px;
    margin-bottom: 10px;
}

.titleExpedicao {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 10px;
}

.titleExpedicao strong {
    color: rgb(141, 137, 137);
}

.aprovar-header {
    margin-bottom: 10px;
}

.cabecalhoExpedicao {
    display: flex;
    flex-direction: row-reverse;
    justify-content: space-between;
}

.eventoFocus:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.07rem #009f3cfa;
}

.nenhumaExpedicao {
    width: 100%;
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    flex-direction: column;
    align-content: center;
    justify-content: space-between;
    padding: 10px;
    border-radius: 10px;
}

.divNenhumaExpedicao {
    width: 50%;
    margin: 0 auto;

    display: flex;
    flex-direction: column;
    align-content: center;
    align-items: center;
    flex-wrap: wrap;
    justify-content: space-between;
    height: 300px;
    border-radius: 10px;


}

.iconeNenhumaExpedicao {
    width: 150px;
    height: 150px;
    margin-top: 80px;
}

.nenhumaExpedicao h2 {
    margin-top: 10px;
    font-size: 20px;
    text-shadow: -3px -3px 5px #00ff5977;
    font-weight: normal;
    margin-bottom: 30px
}

.labelAprovarExpedicao {
    width: 100%;
}

@media screen and (max-width: 656px) {
    .divNenhumaExpedicao {
        width: 100%;
    }
}

@media screen and (max-width: 354px) {
    .divNenhumaExpedicao {
        width: 100%;
    }
}

.botaoAtualizar {
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    border: none;
    border-radius: 5px;
}


.cardTemporarioExpedicao {
    height: 260px;
    opacity: 0;
}