import React from 'react'
import "./card.css"
import { CardDashProps } from '../../../interfaces'

const CardDash: React.FC<CardDashProps> = ({ titulo, children }) => {

  if (!children) {
    return null
  }

  if (typeof children === "string") {
    return (
      <div className="cardDashboard">
        <div className='cabecalhoCard'>
          <h1>{titulo}</h1>
        </div>
        <div className='corpoCard'>
          {children}
        </div>
      </div>
    )
  }

  return (
    <div className="cardDashboard">
      <div className='cabecalhoCard'>
        <h1>{titulo}</h1>
      </div>
      <div className='corpoCard'>
        {children}
      </div>
    </div>
  )
}

export default CardDash;
