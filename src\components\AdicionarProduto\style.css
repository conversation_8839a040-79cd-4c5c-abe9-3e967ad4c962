* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: var(--font-main);
}

.eventoFocus:focus{
    border-color: var(--primary-color) ;
    box-shadow: 0 0 0 0.07rem #009f3cfa ;
}

.divInputPopupAdicionarProduto {
    width: 100%;
    margin-top: 2rem;
    display: flex;
    flex-direction: column;
    margin-bottom: 1rem;
}

.divNomePopupAdicionarProduto {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}


.divPrecoQuantidade {
    width: 100%;
    display: flex;
    padding: auto;
    flex-direction: row;
}

#inputNome {
    width: 97%;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: auto;
    margin-bottom: 10px;
}

.inputAdicionarProduto {
    width: 100%;
    height: var(--input-altura-padrao);
    border-radius: 5px;
    border: none;
    padding: 0 10px;
    outline: none;
}
.divInputPrecoPopupAdicionarProduto {
    width: 48%;
    margin: 0 auto;
    padding: auto;
   color: white;
}

.divInputPrecoPopupAdicionarProduto label {
    color: white;
}

.divInputPrecoPopupAdicionarProduto label {
    color: white;
}


.inputPrecoAdicionarProduto {
    width: 100%;
    height: var(--input-altura-padrao);
    border-radius: 5px;
    border: none;
    padding: 0 10px;
    outline: none;
}

.divInputQuantidade {
    width: 48%;
    margin: 0 auto;
    padding: auto;
    color: white;
   

}

.inputAdicionarQuantidade {
    width: 100%;
    height: var(--input-altura-padrao);
    border-radius: 5px;
    border: none;
    padding: 0 10px;
    outline: none;
}   


.divBotaoAdicionarProduto {
    width: 99%;
    display: flex;
    justify-content: flex-end;
}
.botaoAdicionarProduto {
    width: 100%;
    max-width: 10rem;
    height: 2.5rem;
    border: none;
    border-radius: 0.5rem;
    background-color: var(--primary-color);
    color: white;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: 0.3s;
}