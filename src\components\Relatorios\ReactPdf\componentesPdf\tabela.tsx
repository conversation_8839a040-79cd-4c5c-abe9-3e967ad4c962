import { StyleSheet, Text, View } from "@react-pdf/renderer";
import { ItemPedidoRelatorioVendas } from "../../../../interfaces";

const tabelaEstilo = StyleSheet.create({
  tabela: {
    width: "100%",
    height: "100%",
  },
  cabecalhoTabela: {
    width: "100%",
    backgroundColor: "#e0e0e0",
    height: 25,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    borderBottom: "1px solid black",
    borderLeft: "1px solid black",
    borderRight: "1px solid black",
    flexDirection: "row",
    margin: 0,
  },
  tituloCabecalho: {
    height: "100%",
    margin: 0,
    padding: 0,
    display: "flex",
    justifyContent: "center",
    borderRight: "1px solid black",
    alignItems: "center",
    fontWeight: "bold",
  },
  tituloCabecalhoUltimo: {
    height: "100%",
    margin: 0,
    padding: 0,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    fontWeight: "bold",
  },
  textoTituloCabecalho: {
    fontSize: 10,
    fontWeight: "bold",
    margin: 0,
    padding: 0,
  },
  corpoTabela: {
    width: "100%",
    height: "85%",
    display: "flex",
    justifyContent: "flex-start",
    alignItems: "flex-start",
    flexDirection: "column",
  },
  campoProdutoTabela: {
    display: "flex",
    justifyContent: "center",
    alignItems: "flex-start",
    flexWrap: "wrap",
    padding: 5,
    borderRight: "1px solid black",
  },
  campoProdutoTabelaNumero: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    padding: 5,
    borderRight: "1px solid black",
  },

  campoProdutoTabelaUltimo: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    padding: 5,
  },

  textCampoTabela: {
    fontSize: 10,
    margin: 0,
    padding: 0,
  },
});

const Tabela = ({ itens }: { itens: ItemPedidoRelatorioVendas[] }) => {
  return (
    <>
      <View style={tabelaEstilo.tabela}>
        <View style={tabelaEstilo.cabecalhoTabela}>
          <View style={[tabelaEstilo.tituloCabecalho, { width: "30%" }]}>
            <Text style={tabelaEstilo.textoTituloCabecalho}>Produto</Text>
          </View>
          <View style={[tabelaEstilo.tituloCabecalho, { width: "40%" }]}>
            <Text style={tabelaEstilo.textoTituloCabecalho}>Descrição</Text>
          </View>
          <View style={[tabelaEstilo.tituloCabecalho, { width: "10%" }]}>
            <Text style={tabelaEstilo.textoTituloCabecalho}>Valor Un.</Text>
          </View>
          <View style={[tabelaEstilo.tituloCabecalho, { width: "8%" }]}>
            <Text style={tabelaEstilo.textoTituloCabecalho}>Qtd.</Text>
          </View>
          <View style={[tabelaEstilo.tituloCabecalhoUltimo, { width: "12%" }]}>
            <Text style={tabelaEstilo.textoTituloCabecalho}>Subtotal</Text>
          </View>
        </View>

        <View style={tabelaEstilo.corpoTabela}>
          <View
            key={itens.length}
            style={{ borderLeft: "1px solid black", borderRight: "1px solid black", width: "100%", height: "auto" }}
          >
            <>
              {itens.map((item, index) => (
                <>
                  <View
                    key={index}
                    style={{
                      borderBottom: "1px solid black",
                      width: "100%",
                      height: 25,
                      display: "flex",
                      flexDirection: "row",
                    }}
                  >
                    <View style={[tabelaEstilo.campoProdutoTabela, { width: "30%" }]}>
                      <Text style={tabelaEstilo.textCampoTabela}>{item.produto}</Text>
                    </View>

                    <View style={[tabelaEstilo.campoProdutoTabela, { width: "40%" }]}>
                      <Text style={tabelaEstilo.textCampoTabela}>{item.descricao}</Text>
                    </View>

                    <View style={[tabelaEstilo.campoProdutoTabelaNumero, { width: "10%" }]}>
                      <Text style={tabelaEstilo.textCampoTabela}>
                        {item.quantidade ? (
                          <>
                            {new Intl.NumberFormat("pt-BR", {
                              style: "currency",
                              currency: "BRL",
                            }).format(Number(item.subtotal) / item.quantidade)}
                          </>
                        ) : (
                          <></>
                        )}
                      </Text>
                    </View>

                    <View style={[tabelaEstilo.campoProdutoTabelaNumero, { width: "8%" }]}>
                      <Text style={tabelaEstilo.textCampoTabela}>
                        {item.quantidade === 0 ? <></> : item.quantidade}
                      </Text>
                    </View>

                    <View style={[tabelaEstilo.campoProdutoTabelaUltimo, { width: "12%" }]}>
                      <Text style={tabelaEstilo.textCampoTabela}>
                        {item.subtotal ? (
                          <>
                            {new Intl.NumberFormat("pt-BR", {
                              style: "currency",
                              currency: "BRL",
                            }).format(Number(item.subtotal))}
                          </>
                        ) : (
                          <></>
                        )}
                      </Text>
                    </View>
                  </View>
                </>
              ))}
            </>
          </View>
        </View>
      </View>
    </>
  );
};

export default Tabela;
