import React from "react";
import "./carregamento.css";

export const Carregamento = () => {
    return (
        <div className="componente">
            <div className="container-carregamento">
                <div className="porta-completa">
                <div className="batente">
                    <div className="porta">
                        <div className="fechadura"></div>
                    </div>
              </div>
              <div className="carregamento"><h1>Carregando...</h1></div>
              </div>
           </div>
        </div>
    );
};

interface ComponenteCarregamentoProps {
    carregando: boolean | null;
    children: React.ReactNode;
}

const ComponenteCarregamento = ({
    carregando,
    children,
}: ComponenteCarregamentoProps) => {
    return (
        <>
            {carregando ? <Carregamento /> : children}
        </>
    )
    
};

export default ComponenteCarregamento;


/* 
uso:
    const [carregando, setCarregando] = useState(true);

    <ComponenteCarregamento carregando={carregando}>
        <h1>Conteúdo</h1>
    </ComponenteCarregamento>
*/