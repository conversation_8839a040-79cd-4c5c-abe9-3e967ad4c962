import { toast } from "react-toastify";
import api from "../apiService";

import { AxiosError } from "axios";

import { decodificarTokenRefresh, useAutenticacao } from "../contexts/autenticacao/AuthContext";
import { useNavigate } from "react-router-dom";
import { useState } from "react";
import { IDadosFormulario } from "../interfaces";



const useLogin = () => {
  const { entrar, sair } = useAutenticacao();
  const navegar = useNavigate();
  const [mostrarSenha, definirMostrarSenha] = useState(false);
  const [sign, setSign] = useState(false);
  const [login, setLogin] = useState("");
  const [senha, setSenha] = useState("");

  const aoSubmeter = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
  
    
    if (login === "" || senha === "") {
      toast.dismiss();
      toast.error("Preencha todos os campos");
      return;
    }

    toast.dismiss();
    toast.loading("Efetuando login...");
  
    try {
      
      const manterConectado = document.getElementById(
        "manterConectado"
      ) as HTMLInputElement;

      if (manterConectado.checked) {
        setSign(true);
      }

      const resposta = await autenticarUsuario();
    
      const token = resposta.access_token;
   
      const horasExpToken = decodificarTokenRefresh(token);

      let refreshToken = null;
      let horasExpRefreshToken = undefined;

    
      if (resposta.refresh_token) {
        refreshToken = resposta.refresh_token;
        horasExpRefreshToken = decodificarTokenRefresh(refreshToken);
      }

      entrar(token, horasExpToken,  refreshToken, horasExpRefreshToken);

      setTimeout(() => {
        sair();
      }, horasExpToken * 3600000);

      navegar("/home");

    
      if (resposta.status === 200 || resposta.status === 201) {
        toast.dismiss();
        toast.success("Login efetuado com sucesso!");
      }
    } catch (erroAny) {

  
      const erro = erroAny as AxiosError;
      const resposta = erro.response;


      if (erro.message === "timeout of 10000ms exceeded") {
        toast.dismiss();
        toast.error("Erro de rede!");
      }


      if (erro.message === 'Network Error') {
        toast.dismiss();
        toast.error("Erro de rede!");
      }
      if (resposta?.status === 0) {
        console.error("Erro de rede");
        toast.dismiss();
        toast.error("Erro de rede!");
      }


      if (resposta?.status === 400) {
        console.error("Erro de rede");
        toast.dismiss();
        toast.error("Erro de rede!");
      }

      if (resposta?.status === 500) {
        toast.dismiss();
        toast.error("Erro ao efetuar login!");
      }
      if (resposta?.status === 401) {
        toast.dismiss();
        toast.error((resposta?.data as { message: string }).message);
      }
      if (resposta?.status === 404) {
        toast.dismiss();
        toast.error((resposta?.data as { message: string }).message);
      }
    }
  };

  const autenticarUsuario = async () => {
    const dados = {
      login: login,
      senha: senha,
      sign: sign,
    } as IDadosFormulario;

    const resposta = await api.post("/auth/login", dados, {
      headers: {
        "Content-Type": "application/json",
      },
    });

    return resposta.data;
  };

  const alternarSenha = () => {
    definirMostrarSenha(!mostrarSenha);
  };

  return {
    entrar,
    sair,
    mostrarSenha,
    definirMostrarSenha,
    aoSubmeter,
    autenticarUsuario,
    alternarSenha,
    setLogin,
    setSenha,
    setSign
  };
};

export default useLogin;