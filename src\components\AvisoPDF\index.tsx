import "./aviso.css"
import { PiFilePdfDuotone } from "react-icons/pi";

export const verificarSeNavegadorSuportaPDF = () => {

    const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
    const isFirefox = navigator.userAgent.toLowerCase().indexOf('firefox') > -1;
    const isChrome = /chrom(e|ium)/.test(navigator.userAgent.toLowerCase());
    if (isMobile && (isSafari || isFirefox)) {
        return false;
    } else if (isMobile && isChrome) {
        return false;
    } else {
        return true;
    }
};

const AvisoNaoSuportaPDF = () => {
    return (
        <div className="divNaoSuporta">
            <PiFilePdfDuotone   className="iconeNaoSuportaPDF"/>
            <h1>Seu navegador não suporta visualização direta de PDFs. Por favor, baixe o arquivo usando o botão abaixo</h1>
        </div>
    );
};

export default AvisoNaoSuportaPDF;