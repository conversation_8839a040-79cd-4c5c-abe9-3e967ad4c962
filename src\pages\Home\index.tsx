import {AiOutlineShoppingCart} from "react-icons/ai";
import { IoAlbumsOutline, IoPersonOutline } from "react-icons/io5";
import { FaBars } from "react-icons/fa";
import { TbLogout } from "react-icons/tb";
import { LuLayoutDashboard, LuPackagePlus } from "react-icons/lu";
import { BsPersonFillDown, BsPersonFillUp } from "react-icons/bs";

import "./home.css";
import Logo from "../../assets/images/Logo/FRP_2_BRANCO.png";

import PopupAdicionarProdutos from "../../components/AdicionarProduto";

import { ToastContainer } from "react-toastify";

import {
  ComponenteProtegido,
  UsuarioRoles,
} from "../../contexts/autenticacao/AuthContext";

import { FiPlusCircle } from "react-icons/fi";
import { PiListBulletsFill } from "react-icons/pi";
import useHome from "../../hooks/useHome";

import packageJson from '../../../package.json';

const Home = () => {
  const {
    dadosUsuario,
    sair,
    componenteAtivo,
    abrirMenu,
    setAbrirMenu,
    menuLateral,
    alternarIcone,

    estiloInformacoes,
    setEstiloInformacoes,
    linkAtivo,

    manipularClickMenuDashboard,
    manipularClickMenuTerminalVendas,
    manipularClickRegistroVendas,
    manipularClickMenuProdutos,
    manipularClickExpedição,
    manipularClickAprovarExpedição,
    manipularClickPopupAdicionarProduto,
    manipularClickFecharPopup,
    abirPopupAdicionarProdutos,
    abrirSubmenuUsuarios,
    setAbrirSubmenuUsuarios,
    manipularClickMenuUsuarios,
    alterarIconeFuncao,
    mostrarSecaoInformacoes,
  } = useHome();


  const FormatarNomeRole = (role: string | undefined) => {
    if (role === "EXPEDICAO") {
      return "EXPEDIÇÃO";
    } else {
      return role?.toUpperCase();
    }
  }

  const formatarNomeUsuario = (nome: string | undefined) => {

    return nome?.split(" ")[0]; 

  }


  return (
    <div className="main">
      <div className="top-bar">
        <button
          className="hamburger-button"
          onClick={() => setAbrirMenu(!abrirMenu)}
        >
          <FaBars className="hamburgerIcon" />
        </button>
        <div className="logoFRP">
          <a href="/home">
            <img src={Logo} alt="logo do sistema" />
          </a>
        </div>
        <div className={estiloInformacoes}>
          <div className="secaoInformacoesUsuario">
            <div className="secaoInformacoesNome">
              <p id="nomeUsuario">
                Olá <strong>{formatarNomeUsuario(dadosUsuario?.nomeUsuario)}</strong>
              </p>
            </div>
            <div className="secaoInformacoesRole">
              <p id="roleUsuario">{FormatarNomeRole(dadosUsuario?.roles[0])}</p>
            </div>
          </div>
          <div className="secaoInformacoeslogOut" title="sair" onClick={sair}>
            <TbLogout className="logOutIcon cursor" />
          </div>
        </div>
      </div>
      <button
        className="botaoAbrirSubMenuUsuario"
        onClick={() => {
          if (estiloInformacoes === "mostraSecaoInformacoes") {
            return (
              setEstiloInformacoes("secaoInformacoes"), alterarIconeFuncao()
            );
          } else {
            mostrarSecaoInformacoes();
            alterarIconeFuncao();
          }
        }}
      >
        {alternarIcone ? (
          <BsPersonFillUp className="iconPersonDown" />
        ) : (
          <BsPersonFillDown className="iconPersonDown" />
        )}
      </button>
      <div className="content-container">
        <div
          ref={menuLateral}
          className={`menu ${abrirMenu ? "open" : ""} menuPrincipal`}
        >
          <div className="menuLateral">
            <ul className="menuLista" id="menuLista">
              <ComponenteProtegido rolesNecessarias={[UsuarioRoles.Admin, UsuarioRoles.Gestor, UsuarioRoles.Financeiro, UsuarioRoles.Vendedor]} fallback={<></>}>
                <a href="#" onClick={manipularClickMenuDashboard}>
                  <li className={linkAtivo === "dashboard" ? "active" : ""}>
                    <LuLayoutDashboard /> Dashboard
                  </li>
                </a>

              </ComponenteProtegido>

              <ComponenteProtegido
                rolesNecessarias={[UsuarioRoles.Admin, UsuarioRoles.Vendedor]}
                fallback={<></>}
              >
                <a href="#" onClick={manipularClickMenuTerminalVendas}>
                  <li className={linkAtivo === "terminal" ? "active" : ""}>
                    <AiOutlineShoppingCart /> Terminal de vendas
                  </li>
                </a>
              </ComponenteProtegido>

              <ComponenteProtegido
                rolesNecessarias={[
                  UsuarioRoles.Admin,
                  UsuarioRoles.Financeiro,
                  UsuarioRoles.Gestor,
                  UsuarioRoles.Vendedor,
                ]}
                fallback={<></>}
              >
                <a href="#" onClick={manipularClickRegistroVendas}>
                  <li className={linkAtivo === "registro" ? "active" : ""}>
                    <PiListBulletsFill /> Registros de vendas
                  </li>
                </a>
              </ComponenteProtegido>

              <ComponenteProtegido
                rolesNecessarias={[UsuarioRoles.Admin, UsuarioRoles.Expedicao]}
                fallback={<></>}
              >
                <a href="#" onClick={manipularClickExpedição}>
                  <li className={linkAtivo === "expedicao" ? "active" : ""}>
                    <LuPackagePlus /> Terminal de expedição
                  </li>
                </a>
              </ComponenteProtegido>

              <ComponenteProtegido
                rolesNecessarias={[
                  UsuarioRoles.Admin,
                  UsuarioRoles.Financeiro,
                  UsuarioRoles.Expedicao,
                  UsuarioRoles.Vendedor,
                  UsuarioRoles.Gestor,
                ]}
                fallback={<></>}
              >
                <a href="#" onClick={manipularClickAprovarExpedição}>
                  <li
                    className={linkAtivo === "aprovarExpedicao" ? "active" : ""}
                  >
                    <PiListBulletsFill /> Registros de expedições
                  </li>
                </a>
              </ComponenteProtegido>





              <ComponenteProtegido
                rolesNecessarias={[UsuarioRoles.Admin, UsuarioRoles.Gestor]}
                fallback={<></>}
              >
                <a
                  href="#"
                  onClick={manipularClickMenuUsuarios}
                  className="cursor"
                >
                  <li
                    className={linkAtivo === "usuarios" ? "active" : ""}
                    onClick={() =>
                      setAbrirSubmenuUsuarios(!abrirSubmenuUsuarios)
                    }
                  >
                    <IoPersonOutline /> Usuários
                    {/* {abrirSubmenuUsuarios && (

                        <ComponenteProtegido
                          rolesNecessarias={[
                            UsuarioRoles.Admin,
                            UsuarioRoles.Gestor
                          ]}
                          fallback={<></>}
                        >
                        </ComponenteProtegido>

                      )} */}
                  </li>
                </a>
              </ComponenteProtegido>
              <ComponenteProtegido
                rolesNecessarias={[
                  UsuarioRoles.Admin,
                  UsuarioRoles.Financeiro,
                  UsuarioRoles.Gestor,
                  UsuarioRoles.Vendedor,
                  UsuarioRoles.Expedicao,
                ]}
                fallback={<></>}
              >
                <a href="/" onClick={manipularClickMenuProdutos}>
                  <li className={linkAtivo === "lista" ? "active" : ""}>
                    <IoAlbumsOutline /> Produtos
                  </li>
                </a>
              </ComponenteProtegido>

              <ComponenteProtegido
                rolesNecessarias={[UsuarioRoles.Admin, UsuarioRoles.Expedicao]}
                fallback={<></>}
              >
                <a href="#" onClick={manipularClickPopupAdicionarProduto}>
                  <li>
                    <FiPlusCircle /> Adicionar produto
                  </li>
                </a>
              </ComponenteProtegido>
            </ul>
            <p className="paragrafoVersao">Versão: {packageJson.version}</p>
          </div>
        </div>

        <div id="conteudoContainer" className="Conteudo conteudo-container">
          <div className="divConteudoContainer">{componenteAtivo}</div>
        </div>
      </div>
      <PopupAdicionarProdutos
        mostrar={abirPopupAdicionarProdutos}
        fechar={manipularClickFecharPopup}
      />

      <ToastContainer
        limit={3}
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="dark"
      />
    </div>
  );
};

export default Home;
