import { View } from "@react-pdf/renderer";
import { DadosRelatorioVendas, ItemPedidoRelatorioVendas } from "../../../../interfaces";
import { InformacoesPedido } from "./informacoesPedido";
import { CabecalhoPdf } from "./cabecalho";
import { ItensPedido } from "./itensPedido";


export const BaseRelatorio = ({ dados, itens, index }: { dados: DadosRelatorioVendas, itens: ItemPedidoRelatorioVendas[], index: number }) => {

    const dataFormatada = new Date(dados.criadoEm).toLocaleDateString("pt-BR", {
        timeZone: "UTC",
    });


    const totalFormatado = new Intl.NumberFormat("pt-BR", {
        style: "currency",
        currency: "BRL",
    }).format(Number(dados.total));


    return (
        <>
            <View style={{ width: "100%", height: "100%", display: "flex", justifyContent: "flex-start", alignItems: "flex-start", flexDirection: "column", padding: 5 }}>
                <CabecalhoPdf id={dados.id} />
                <InformacoesPedido nomeVendedor={dados.vendedor} nomeCliente={dados.cliente} formaPagamento={dados.pagamento} dataImpressao={dataFormatada} />
                <ItensPedido itens={itens} total={totalFormatado} index={index} />
            </View>
        </>
    )
}