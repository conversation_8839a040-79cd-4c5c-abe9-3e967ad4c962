import { useEffect, useState } from "react";
import CardDash from "../card";
import { FormaPagamentosMaisUsadasProps } from "../../../interfaces";


const FormaPagamentosMaisUsadas: React.FC<FormaPagamentosMaisUsadasProps> = ({ dados }) => {
   
  const [contagemPagamentos, setContagemPagamentos] = useState<{[key: string]: number;}>({});

    useEffect(() => {
        contagemPagamentosPedidos();
      }, [dados]);

      
    const contagemPagamentosPedidos = () => {
        if (dados) {
          const contagemPagamentosNova = dados.reduce((acc, pedido) => {
            const pagamento = pedido.pagamento;
            acc[pagamento] = (acc[pagamento] || 0) + 1;
            return acc;
          }, {} as { [key: string]: number });
          
          const top5Pagamentos = Object.keys(contagemPagamentosNova)
            .sort((a, b) => contagemPagamentosNova[b] - contagemPagamentosNova[a])
            .slice(0, 5);
    
          const contagemPagamentosTop5 = top5Pagamentos.reduce((acc, pagamento) => {
            acc[pagamento] = contagemPagamentosNova[pagamento];
            return acc;
          }, {} as { [key: string]: number });
    
          setContagemPagamentos(contagemPagamentosTop5);
        }
      };
  


    if (Object.keys(contagemPagamentos).length === 0 || dados === null || dados.length === 0) {
      return (
          <>
          <CardDash titulo="Formas de pagamento mais usadas">
            <div className="divCardDashboard divCardVazio">
              <ul className="ulCardVazio">
                <li className="liCardVazio">
                  Não há dados para exibir  
                </li>
              </ul>
            </div>
          </CardDash>
        </>
      )
    }

      

    return (<>
        <CardDash titulo="Formas de pagamento mais usadas">
            <div className="divCardDashboard">
                <ul className="ulCardDashboard">
                    {Object.keys(contagemPagamentos).map((formaPagamento, index) => (
                        <li key={index} className="liCardDashboard">
                            {formaPagamento} 
                            <span className="spanCardMonetario">{contagemPagamentos[formaPagamento]} {contagemPagamentos[formaPagamento] > 1 ? 'vezes' : 'vez'}</span> 
                        </li>
                    ))}
                
                </ul>
            </div>
        </CardDash>
    </>)
}

export default FormaPagamentosMaisUsadas;