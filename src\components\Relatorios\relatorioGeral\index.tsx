
import {
  Document,
  Page,
  Text,
  View,
  StyleSheet,
  Image,
} from "@react-pdf/renderer";
import Logo from "../../../assets/images/Logo/FRP_PRETO.png";
import { format } from "date-fns";
import { Pedido } from "../../../interfaces";


const estilos = StyleSheet.create({
  pagina: {
    backgroundColor: "#FFF",
    color: "black",
  },
  visualizador: {
    width: "100%",
    height: "70vh",
  },
  logo: {
    width: 110,
    height: 60,
    margin: 10,
    marginLeft: 30,
  },
  cabecalho: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 20,
  },
  titulo: {
    fontSize: 18,
    marginLeft: 50,
    fontWeight: "bold",
  },
  caixaProposta: {
    border: "1px solid black",
    width: 130,
    height: 60,
    marginLeft: 100,
    justifyContent: "center",
    padding: 5,
  },
  textoProposta: {
    fontSize: 10,
    marginBottom: 15,
  },
  idProposta: {
    fontSize: 16,
    textAlign: "center",
    marginBottom: 10,
  },
  linha: {
    borderBottom: "1px solid black",
    marginBottom: 15,
    marginRight: 5,
    marginLeft: 5,
    marginTop: -25,
  },
  textoPequeno: {
    fontSize: 10,
  },
  textoNegrito: {
    fontWeight: "bold",
  },
  caixaVendedor: {
    marginTop: -10,
    marginLeft: 10,
  },
  linha2: {
    borderBottom: "1px solid black",
    marginBottom: 15,
    marginRight: 5,
    marginLeft: 5,
  },
  tituloInfoPedido: {
    fontSize: 18,
    marginTop: -10,
    marginLeft: "auto",
    marginRight: "auto",
    fontWeight: "bold",
    marginBottom: 5,
  },
  linhaInfoPedido: {
    borderBottom: "1px solid black",
    marginBottom: 15,
    marginRight: 5,
    marginLeft: 5,
  },
  secaoInfoPedido: {
    marginTop: -10,
    fontSize: 10,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  total: {
    fontSize: 10,
    marginLeft: 10,
  },
  pagamento: {
    fontSize: 10,
  },
  data: {
    fontSize: 10,
    marginRight: 15,
  },
  tabela: {
    display: "flex",
    flexDirection: "column",
    width: "96%",
    justifyContent: "center",
    border: "1px solid black",
    borderRadius: 0,
    margin: 10,
  },
  larguraCampoId: {
    width: 70,
    padding: 5,
    textAlign: "center",
    fontSize: 10,
    flexWrap: "wrap",
  },
  celularLarguraCliente: {
    width: "30%",
    padding: 5,
    fontSize: 10,
    flexWrap: "wrap",
  },
  cabecalhoTabela: {
    display: "flex",
    flexDirection: "row",
    backgroundColor: "#e0e0e0",
    borderBottom: "1px solid black",
  },
  linhaTabela: {
    display: "flex",
    flexDirection: "row",
  },
  celulaPagamento: {
    width: "20%",
    padding: 5,
    fontSize: 10,
    flexWrap: "wrap",
  },
  celulaNumerica: {
    width: "5%",
    padding: 5,
    fontSize: 10,
    textAlign: "right",
    flexWrap: "wrap",
  },
  linhaCentral: {
    position: "absolute",
    left: 0,
    top: "50%",
    width: "100%",
    borderTop: "1px dashed #d3d3d3 ",
  },
  conteudoDuplicado: {
    position: "absolute",
    top: "50%",
    width: "100%",
    borderTop: "1px solid black",
  },
  numeroPagina: {
    position: "absolute",
    fontSize: 8,
    
    bottom: 5,
    right: 10,
  },
  valorTotal: {
    width: "10%",
    padding: 5,
    fontSize: 10,
    textAlign: "right",
    flexWrap: "wrap",
  },
  datas: {
    flexDirection: "row",
    justifyContent: "center",
    marginTop: "-5px",
  },
  tituloPeriodo: {

    fontSize: 10,
    color: "black",
    fontWeight: "bold",
  },
  textoNaoInformado: {
    color: "#696969"
  }
});

const RelatorioGeral = ({
  dados,
  dataInicial,
  dataFinal,
}: {
  dados: Pedido[];
  dataInicial: Date | null | undefined;
  dataFinal: Date | null | undefined;
}) => {


  const ITEMS_PER_PAGE = 20;

  const paginatedData = [];
  for (let i = 0; i < dados.length; i += ITEMS_PER_PAGE) {
    paginatedData.push(dados.slice(i, i + ITEMS_PER_PAGE));
  }

  const totalSoma = dados.reduce(
    (acc, pedido) => acc + Number(pedido.total),
    0
  );

// converter a data para dia/mes/ano
  const dataInicialFormatada = dataInicial ? format(dataInicial, "dd/MM/yyyy") : "";
  const dataFinalFormatada = dataFinal ? format(dataFinal, "dd/MM/yyyy") : "";

  const totalSomaSemLivreDebito = dados
    .filter((pedido) => pedido.pagamento !== "Livre de débito")
    .reduce((acc, pedido) => acc + Number(pedido.total), 0);


    
  const totalSomaLivreDebito = dados
    .filter((pedido) => pedido.pagamento === "Livre de débito")
    .reduce((acc, pedido) => acc + Number(pedido.total), 0);

  return (
    <>
        <Document>
          {paginatedData.map((pageData, pageIndex) => (
            <Page size="A4" orientation="landscape" key={`${pageData}`} style={estilos.pagina}>
              
              <View>
                <View style={estilos.cabecalho}>
                  <Image src={Logo} style={estilos.logo} />
                  <Text style={estilos.titulo}>
                    RELATÓRIO DE VENDAS POR FECHAMENTO
                  </Text>
                  <View style={estilos.caixaProposta}>
                    <Text style={estilos.textoProposta}>Gerado dia:</Text>
                    <Text style={estilos.idProposta}>
                      {new Intl.DateTimeFormat("pt-BR").format(new Date())}
                    </Text>
                  </View>
                </View>
                <View style={estilos.linha}></View>
                <View style={estilos.datas}>
                <Text style={estilos.tituloPeriodo}>
                  {dataInicialFormatada} a {dataFinalFormatada}
                </Text>
              </View>

                <View style={estilos.tabela}>
                  <View style={estilos.cabecalhoTabela}>
                    <Text
                      style={{
                        ...estilos.larguraCampoId,
                        borderRight: "1px solid black",
                      }}
                    >
                      N° Proposta
                    </Text>
                    <Text
                      style={{
                        ...estilos.celularLarguraCliente,
                        borderRight: "1px solid black",
                      }}
                    >
                      Cliente
                    </Text>
                    <Text
                      style={{
                        ...estilos.celularLarguraCliente,
                        borderRight: "1px solid black",
                      }}
                    >
                      Vendedor
                    </Text>
                    <Text
                      style={{
                        ...estilos.celulaPagamento,
                        borderRight: "1px solid black",
                      }}
                    >
                      Condição de Pagamento
                    </Text>
                    <Text
                      style={{
                        ...estilos.celulaNumerica,
                        borderRight: " solid black",
                      }}
                    >
                      Total
                    </Text>
                  </View>
                  {pageData.map((pedido, index) => (
                    <View
                      style={{
                        ...estilos.linhaTabela,
                        borderBottom: "0.5px solid black",
                      }}
                      key={index}
                    >
                      <View
                        style={{
                          ...estilos.larguraCampoId,
                          borderRight: "1px solid black",
                        }}
                      >
                        <Text>{pedido.id}</Text>
                      </View>
                      <View
                        style={{
                          ...estilos.celularLarguraCliente,
                          borderRight: "1px solid black",
                          color: pedido.cliente ? "black" : "#696969",
                        }}
                      >
                        <Text> {pedido.cliente || "Não informado"}</Text>
                      </View>

                      <View
                        style={{
                          ...estilos.celularLarguraCliente,
                          borderRight: "1px solid black",
                        }}
                      >
                        <Text>{pedido.vendedor}</Text>
                      </View>
                      <View
                        style={{
                          ...estilos.celulaPagamento,
                          borderRight: "1px solid black",
                        }}
                      >
                        <Text>
                          {pedido.pagamento === "Crédito"
                            ? `${pedido.pagamento} (${pedido.parcelamento} ${
                                pedido.parcelamento === 1
                                  ? "parcela"
                                  : "parcelas"
                              })`
                            : pedido.pagamento}
                        </Text>
                      </View>

                      <View style={estilos.celulaNumerica}>
                        <Text>
                          {new Intl.NumberFormat("pt-BR", {
                            style: "currency",
                            currency: "BRL",
                          }).format(Number(pedido.total))}
                        </Text>
                      </View>
                    </View>
                  ))}
                </View>
                {pageIndex === paginatedData.length - 1 && (
                  <View
                    style={{
                      flexDirection: "row",
                      justifyContent: "flex-end",
                      marginRight: "80px",
                    }}
                  >
                    <Text style={estilos.valorTotal}>Descontos:</Text>
                    <Text style={estilos.celulaNumerica}>
                      {new Intl.NumberFormat("pt-BR", {
                        style: "currency",
                        currency: "BRL",
                      }).format(totalSomaLivreDebito)}
                    </Text>
                    <Text style={estilos.valorTotal}>Subtotal:</Text>
                    <Text style={estilos.celulaNumerica}>
                      {new Intl.NumberFormat("pt-BR", {
                        style: "currency",
                        currency: "BRL",
                      }).format(totalSoma)}
                    </Text>
                    <Text style={estilos.valorTotal}>Total:</Text>
                    <Text style={estilos.celulaNumerica}>
                      {new Intl.NumberFormat("pt-BR", {
                        style: "currency",
                        currency: "BRL",
                      }).format(totalSomaSemLivreDebito)}
                    </Text>
                 
                  </View>
                )}
               
              </View>
              <Text style={estilos.numeroPagina} fixed>{pageIndex + 1}</Text>
            </Page>
          ))}
        </Document>
    </>
  );
};



export default RelatorioGeral;
