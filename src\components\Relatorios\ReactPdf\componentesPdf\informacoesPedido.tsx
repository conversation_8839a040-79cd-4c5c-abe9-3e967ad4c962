import { View, Text, StyleSheet  } from "@react-pdf/renderer"

export const InformacoesPedido = ({ nomeVendedor, nomeCliente, formaPagamento, dataImpressao }: { nomeVendedor: string, nomeCliente: string, formaPagamento: string, dataImpressao: string }) => {

    const formatarNome = (nome: string) => {
        const nomes = nome.split(" ");
        return `${nomes[0]} ${nomes[nomes.length - 1]}`
    }

    return (
        <>
            <View style={InformacoesPedidoEstilo.container}>
                <View style={InformacoesPedidoEstilo.corpo}>
                    <Text style={{ fontSize: 10 }}>VENDEDOR: <Text style={{ fontWeight: "ultrabold" }}>{formatarNome(nomeVendedor)}</Text></Text>
                    <Text style={{ fontSize: 10 }}>CLIENTE: <Text style={{ fontWeight: "ultrabold" }}>{nomeCliente === "" || nomeCliente === undefined ? <Text style={{ fontWeight: "ultrabold", color: "black" }}>Não informado</Text> : nomeCliente}</Text></Text>
                    <Text style={{ fontSize: 10 }}>PAGAMENTO EM: <Text style={{ fontWeight: "ultrabold" }}>{formaPagamento}</Text></Text>
                    <Text style={{ fontSize: 10 }}>CRIADO EM: <Text style={{ fontWeight: "ultrabold" }}>{dataImpressao}</Text></Text>
                </View>
            </View>
            <View style={InformacoesPedidoEstilo.tituloInformacoes}>
                <Text style={InformacoesPedidoEstilo.tituloInformacoesText}>INFORMAÇÃO DO PEDIDO</Text>
            </View>
        </>
    )
}

const InformacoesPedidoEstilo = StyleSheet.create({
    container: {
        width: "100%",
        height: 30,
        borderBottom: "1px solid black",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        flexDirection: "column",
        padding: 5,
    },
    corpo: {
        width: "100%",
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        flexDirection: "row",
    },

    tituloInformacoes: {
        width: "100%",
        height: 30,
        borderBottom: "1px solid black",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        flexDirection: "column",
    },
    tituloInformacoesText: {
        fontSize: 18, 
        fontWeight: "bold" 
    }
})