import { useState } from "react";
import { AiOutlinePlus } from "react-icons/ai";
import { RiSendPlaneFill } from "react-icons/ri";
import { SlClose } from "react-icons/sl";
import Select from "react-select";
import "./venda.css";

import "react-toastify/dist/ReactToastify.css";
import ImageCaptureComponent from "./Camera/Camera";
import { FormaPagamento } from "./geral/FormaPagamentoEnum";
// import { adicionarItem } from "./itensPedido/AlteracaoItens/adicionarItem
// import { arredondarParaDuasCasasDecimais } from "./itensPedido/CalculosValores/arredondarParaDuasCasasDecimais";
import useTerminalVendas from "../../hooks/useTerminalVendas";
import { manipularImagemCapturada } from "./Camera/manipularImagemCapturada";

export interface pagamentoSelecionado {
	label: string;
	value: string;
}

const Venda = () => {
	const [enviandoPedido, setEnviandoPedido] = useState(false);
	const {
		dadosUsuario,
		produtos,
		adicionarItem,
		removerItem,
		pagamento,
		setPagamento,
		cliente,
		setCliente,
		itensPedido,
		valoresUnitarios,
		descontosPorcentagem,
		total,
		itemAberto,
		setParcelas,
		setArquivo,
		imagemCapturada,
		setImagemCapturada,
		produtoSelecionado,
		setProdutoSelecionado,
		parcelasSelecionadas,
		setParcelasSelecionadas,
		pagamentoSelecionado,
		setPagamentoSelecionado,
		enviarPedido,
		manipularAlteracaoProduto,
		manipularDescontoReais,
		manipularDescontoPorcentagem,
		mascaraReal,
		handleBlur,
		janelaItemVenda,
		manipularAlteracaoItem,
		setValoresUnitarios,
	} = useTerminalVendas();

	return (
		<>
			<form id="formTerminalVendas" key="FormularioTerminalVendas">
				<div className="terminalVenda expedição-header">
					<div className="CabecalhoTerminalVendas">
						<div className="inputVendedor">
							<div>
								<label className="labelCemPorCento">
									Vendedor:
									<input
										className="eventoFocus inputVendedorInput"
										value={dadosUsuario?.nomeUsuario || "Nome do Usuário Não Disponível"}
										readOnly
									/>
								</label>
							</div>
						</div>

						{pagamento !== FormaPagamento.Credito ? (
							<>
								{" "}
								<div className="inputFormaPagamento">
									<div>
										<label className="labelTerminalVendas textNoWrap">
											Forma de pagamento:
											<Select
												id="selectFormaPagamento"
												className="selectTerminalVendas textNoWrap"
												isSearchable={false}
												theme={theme => ({
													...theme,

													colors: {
														...theme.colors,
														primary25: "#009f311c",
														primary: "#009f3c",
														primary50: "#009f311c",
														secundary: "black",
														cursor: "pointer",
														danger: "#ff0000",
														dangerLight: "#ff0000",
													},
												})}
												options={Object.values(FormaPagamento).map(p => ({
													label: p,
													value: p,
												}))}
												onChange={opcao => {
													opcao && setPagamento(opcao.value);
													const pagamentoSelecionado = opcao;
													setPagamentoSelecionado(pagamentoSelecionado);
												}}
												onBlur={handleBlur}
												value={pagamentoSelecionado}
												placeholder="Selecione a forma de pagamento"
												styles={{
													option: provided => ({
														...provided,
														color: "black",
														cursor: "pointer",
													}),
													control: styles => ({
														...styles,
														cursor: "pointer",
													}),
												}}
											/>
										</label>
									</div>
								</div>
							</>
						) : (
							<>
								{" "}
								<div className="inputFormaPagamentoParcelas">
									<div>
										<label className="labelTerminalVendas textNoWrap">
											Forma de pagamento:
											<Select
												id="selectFormaPagamento"
												isSearchable={false}
												className="selectTerminalVendas textNoWrap"
												theme={theme => ({
													...theme,
													colors: {
														...theme.colors,
														primary25: "#009f311c",
														primary: "#009f3c",
													},
												})}
												options={Object.values(FormaPagamento).map(p => ({
													label: p,
													value: p,
												}))}
												onChange={opcao => {
													opcao && setPagamento(opcao.value);
													const pagamentoSelecionado = opcao;
													setPagamentoSelecionado(pagamentoSelecionado);
												}}
												onBlur={handleBlur}
												value={pagamentoSelecionado}
												placeholder="Selecione a forma de pagamento"
												styles={{
													option: provided => ({
														...provided,
														color: "black",
														cursor: "pointer",
													}),
													control: styles => ({
														...styles,
														cursor: "pointer",
													}),
												}}
											/>
										</label>
									</div>
								</div>
							</>
						)}

						{pagamento === FormaPagamento.Credito && (
							<div className="divInputParcelas">
								<div>
									<label className="labelTerminalVendas">
										Quantas vezes?
										<Select
											className="selectTerminalVendas textNoWrap"
											isSearchable={false}
											theme={theme => ({
												...theme,
												colors: {
													...theme.colors,
													primary25: "#009f311c",
													primary: "#009f3c",
												},
											})}
											value={parcelasSelecionadas}
											options={[
												{ label: "1x", value: 1 },
												{ label: "2x", value: 2 },
												{ label: "3x", value: 3 },
												{ label: "4x", value: 4 },
												{ label: "5x", value: 5 },
												{ label: "6x", value: 6 },
											]}
											onChange={opcao => {
												setParcelasSelecionadas(opcao);
												opcao && setParcelas(opcao.value);
											}}
											placeholder="Selecione o número de parcelas"
											styles={{
												option: provided => ({
													...provided,
													color: "black",
													cursor: "pointer",
												}),
												control: styles => ({
													...styles,
													cursor: "pointer",
												}),
											}}
										/>
									</label>
								</div>
							</div>
						)}

						<div className="inputDataTerminal">
							<div>
								<label className="labelCemPorCento">
									Data:
									<input className="inputDataInput" value={new Date().toLocaleDateString()} readOnly />
								</label>
							</div>
						</div>

						<div className="inputTotal">
							<div>
								<label className="labelHeader2">
									Total:
									<input className="inputTotalInput" type="text" value={mascaraReal(total)} readOnly />
								</label>
							</div>
						</div>

						<div className="inputCliente">
							<label className="labelHeader2 labelCliente">
								Cliente:
								<input
									className="inputClienteInput"
									type="text"
									value={cliente}
									onChange={e => setCliente(e.target.value)}
									placeholder="Nome do cliente (opcional)"
								/>
							</label>
						</div>

						<div className="centered-container divImagem">
							<ImageCaptureComponent
								onCapture={(dataUri: string) => {
									manipularImagemCapturada(dataUri, setImagemCapturada, setArquivo);
								}}
							/>
							{imagemCapturada && (
								<div id="divImagem" className="divBordasImagem">
									<img src={imagemCapturada} alt="Capturada" />
								</div>
							)}
						</div>
					</div>

					<div className="divItensPedido">
						{itensPedido.map((item, index) => (
							<div
								key={`${item.id}`}
								id={"itemVenda" + index}
								className={`add-produto itemVenda ${itemAberto === index ? "item-aberto" : ""}`}
								onClick={() => janelaItemVenda(index)}
							>
								<div className="divHeaderItemVenda">
									<div className="selectProduto">
										<div>
											<label className="labelProduto labelTerminalVendas">
												Produto:
												<Select
													className="selectTerminalVendas"
													isSearchable={false}
													id="selectProduto"
													theme={theme => ({
														...theme,
														colors: {
															...theme.colors,
															primary25: "#009f311c",
															primary: "#009f3c",
														},
													})}
													value={produtoSelecionado[item.produtoId || 0]}
													options={Object.values(produtos).map(p => ({
														id: p.id,
														label: p.label,
														valor: p.valor,
														value: p.id,
													}))}
													onChange={opcao => {
														const novosProdutosSelecionados = [...produtoSelecionado];
														// novosProdutosSelecionados[index] = opcao;
														setProdutoSelecionado(novosProdutosSelecionados);
														//////////////
														opcao && manipularAlteracaoProduto(index, opcao.id);
													}}
													placeholder="Selecione um produto"
													styles={{
														option: provided => ({
															...provided,
															color: "black",
															cursor: "pointer",
														}),
														control: styles => ({
															...styles,
															cursor: "pointer",
														}),
													}}
												/>
											</label>
										</div>
									</div>

									<div className="inputValorUnitario add-produto-minimizado add-produto-none">
										<div className="divInputUnitario">
											<label className="labelTerminalVendas">
												Valor unitário:
												<div className="input-group-desabilitado">
													<div className="input-group-desabilitado">
														<input
															type="number"
															className={`valorUnitarioAtivo ${
																itensPedido[index].produtoId === 40 ? "" : "input-desabilitado"
															}`}
															placeholder="Valor Unitário"
															value={valoresUnitarios[itemAberto ?? 0] || ""}
															readOnly={itensPedido[itemAberto || 0]?.produtoId !== 40}
															onChange={e => {
																if (itensPedido[index].produtoId === 40) {
																	const inputValue = e.target.value;
																	const novoValor = inputValue !== "" ? Number(inputValue) : 0;
																	const novosValoresUnitarios = [...valoresUnitarios];
																	novosValoresUnitarios[index] = novoValor;
																	setValoresUnitarios(novosValoresUnitarios);
																	manipularAlteracaoItem(index, "precoGenerico", novoValor);
																}
															}}
														/>
													</div>
												</div>
											</label>
										</div>
									</div>

									<div className="quantidadeInput add-produto-minimizado add-produto-none">
										<div>
											<label className="labelTerminalVendas">
												Quantidade:
												<input
													className="eventoFocus quantidadeInputInput"
													type="number"
													value={item.quantidade || 1}
													defaultValue={item.quantidade || 1}
													onFocus={e => {
														e.target.value = "";
													}}
													onBlur={e => {
														const inputValue = e.target.value;
														if (inputValue === "") {
															e.target.value = "1";
															manipularAlteracaoItem(index, "quantidade", 1);
														}
													}}
													onChange={e => {
														manipularAlteracaoItem(index, "quantidade", parseInt(e.target.value) || 1);
													}}
													placeholder="Quantidade"
												/>
											</label>
										</div>
									</div>
								</div>

								<div className="divMainItemVenda add-produto-minimizado add-produto-none">
									<div className="inputDescricao">
										<div>
											<label className="labelTerminalVendas">
												Descrição:
												<textarea
													className="eventoFocus inputDescricaoInput"
													value={item.descricao || ""}
													onChange={e => manipularAlteracaoItem(index, "descricao", e.target.value)}
													placeholder="Descrição"
													maxLength={100}
												/>
											</label>
										</div>
									</div>
									{item.descricao && item.descricao.length > 99 && (
										<span
											style={{
												color: "red",
											}}
										>
											Você atingiu o limite de caracteres
										</span>
									)}
								</div>

								<div className="divFooterItemVendas add-produto-minimizado add-produto-none">
									<div className="inputDescontos">
										<div className="inputDescontosReais">
											<label className="labelTerminalVendas">
												Desconto em reais:
												{/* <InputGroup>
													<InputGroup.Text>
														R$
													</InputGroup.Text> */}
												<input
													className="eventoFocus inputDescontosReaisInput"
													type="number"
													// value={
													// 	item.desconto !== null
													// 		? arredondarParaDuasCasasDecimais(
													// 			item.desconto
													// 		)
													// 		: ""
													// }
													value={item.desconto || ""}
													onChange={e => {
														const inputValue = e.target.value;
														const novoValor = inputValue !== "" ? parseFloat(inputValue) : null;
														manipularAlteracaoItem(index, "desconto", novoValor || 0);
														manipularDescontoReais(Number(inputValue));
													}}
													placeholder="Desconto"
												/>
												{/* </InputGroup> */}
											</label>
										</div>
									</div>

									<div className="inputDescontoPorcentagem">
										<div>
											<label className="labelTerminalVendas">
												Desconto em %:
												<input
													className="eventoFocus inputDescontoPorcentagemInput"
													type="number"
													value={descontosPorcentagem[index] || ""}
													onChange={e => {
														const inputValue = e.target.value;
														// const novoValor =
														//   inputValue !== "" ? parseFloat(inputValue) : null;
														manipularDescontoPorcentagem(Number(inputValue));
													}}
													placeholder="Desconto em %"
												/>
											</label>
										</div>
									</div>
									<div className="inputValorUnitarioDesconto">
										<div>
											<label className="labelTerminalVendas">
												Valor un. com desc.:
												<div className="input-group-desabilitado">
													<input
														type="string"
														className="input-desabilitado valorUnitarioDescontoInput"
														value={mascaraReal(valoresUnitarios[index] - item.desconto)}
														readOnly
														placeholder="Valor Unitário com Desconto"
													/>
												</div>
											</label>
										</div>
									</div>

									<div className="inputSubtotalItemVenda">
										<div>
											<label className="labelTerminalVendas">
												Subtotal:
												<div className="input-group-desabilitado">
													{/* <InputGroup.Text className="label-desabilitada">R$</InputGroup.Text> */}
													<input
														type="string"
														className="input-desabilitado inputSubtotalItemVendaInput"
														value={mascaraReal(item.subtotal)}
														readOnly
														onChange={e => manipularAlteracaoItem(index, "subtotal", e.target.value)}
														placeholder="Subtotal"
													/>
												</div>
											</label>
										</div>
									</div>
								</div>
								{itensPedido.length > 1 && (
									<button className="botaoRemoverItem" onClick={() => removerItem(index)}>
										<SlClose />
									</button>
								)}
							</div>
						))}
					</div>
					<div className="footer"></div>
					<div className="botoesPedido">
						<button
							className="adicionarProdutoBotao botoesTerminalVendas"
							onClick={e => {
								e.preventDefault();

								adicionarItem();
							}}
						>
							<AiOutlinePlus className="adicionarProdutoIcone" /> <p>Adicionar item</p>
						</button>

						<button
							className="enviarPedidoBotao botoesTerminalVendas"
							disabled={enviandoPedido}
							onClick={async e => {
								e.preventDefault();
								if (enviandoPedido) return;

								setEnviandoPedido(true);
								try {
									await enviarPedido();
								} finally {
									setEnviandoPedido(false);
								}
							}}
						>
							<p>{enviandoPedido ? "Enviando..." : "Enviar pedido"}</p> <RiSendPlaneFill className="enviarPedidoIcone" />
						</button>
					</div>
				</div>
			</form>
		</>
	);
};

export default Venda;
