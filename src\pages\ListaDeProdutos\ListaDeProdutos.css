
.cardProduto {
    width: 100%;
    min-height: 130px;
    height: 100%;
    position: relative;
    padding: 10px;
    background-color: black;
    border-radius: 5px;
    color: white;
    box-shadow: 1px 1px 5px 5px rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: row;
    border: 1px solid var(--primary-color);
    margin-bottom: 0px !important;
}

.cardAltura {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}


.cardNomeProduto {
    font-size: 1.5rem;
    max-width: 85%;
    /* white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis; */
    font-weight: bold;
    -webkit-touch-callout: text;
    -webkit-user-select: text;
    -khtml-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
   
  
}

.cardNomeProduto::selection {
    background-color: rgba(0, 100, 0, 0.452);
}


@media only screen and (max-width: 768px) {
   .cardNomeProduto {
       font-size: 20px;
   }

   .itemProduto {
       width: 45%;
   }
}

.cardAltura p {
    font-size: 16px;
    padding: 5px 0;
    flex: 1;
}

.cardAltura {
   height: 100%;
}

.itemProduto {
    display: flex;
    margin: 4px;
    justify-content: center;
    align-items: center;
    width: 23%;
    height: 100%;
}



.botaoExcluiProduto {
    position: relative;
  
    z-index: 5 !important;
}

.row {
    display: flex;
    flex-wrap: wrap;
    height: auto;
    width: 100%;

}

.paddingEsquerda {
    width: 100%;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;

}

.marginZero {
    margin: 0;
}

.cardListaProduto {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
}





.divSemProdutoCadastrado {
    display: flex;

    align-items: center;
    justify-content: center;
    height: 100%;
}

.divProdutos {
    max-width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
}

.iconeSemProdutoCadastrado {
    font-size: 100px;
    position: relative;
    width: 100px;
}

.divIconeSemProdutoCadastrado {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--primary-color);
    padding: 10px;
    border-radius: 10px;
    height: 100%;
}

.divIconeSemProdutoCadastrado p {
    margin-top: 10px;
    font-size: 20px;
    font-weight: bold;
    color: aliceblue;
}

.botaoExcluirProduto {
    position: absolute;
    background-color: var(--status-cancelado-color);
    border: 1px solid var(--status-cancelado-color);
    top: 0;
    right: 0;
    /* z-index: 5; */
    border-radius: 5px;
    margin: 5px;
    color: aliceblue;
    font-size: 20px;
    padding: 5px;
    cursor: pointer;
    width: 40px;
}


.botaoExcluirProduto:hover {
    background-color: var(--background-status-cancelado-color);
}

@media screen and (max-width: 1543px) {
    .itemProduto {
        width: 30%;
    }
    
}



@media only screen and (max-width: 1080px) {
   .itemProduto {
       width: 45%;
   }
}

@media only screen and (max-width: 650px) {
   .itemProduto {
       width: 90%;
   }
}

.calculoAltura {
    height: auto;  
}

.cardListaProduto {
    width: 100%;
    height: 100%;
}

.divIcone {
    display: flex;
    position: absolute;
    right: 30px;
    bottom: 0;
    align-items: center;
    justify-content: flex-end;
    height: 100%;
    flex-direction: column;
    flex-wrap: wrap;
}

.iconeProduto {
 
    color: rgba(255, 255, 255, 0.15);
    width: 120px;
    height: 120px;
    z-index: 0;
    bottom: 0;
    opacity: 0.5;
}

.bodyCardInformacoes {
    height: 100px;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 10px;
    bottom: 0;
}