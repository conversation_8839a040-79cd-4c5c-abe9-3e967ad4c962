import { toast } from "react-toastify";
import { useEffect, useState } from "react";
import Cookies from "js-cookie";
import api from "../apiService";
import { ExpedicaoRegistro } from "../interfaces";

const useAprovarExpedicao = () => {
  const [expedicao, setExpedições] = useState<ExpedicaoRegistro | undefined>();
  const [status, setStatus] = useState<string>("Pendente");
  const [quantidadeFiltro, setQuantidade] = useState<number>(10);
  const [filtro, setFiltro] = useState<string>("DESC");
  const [paginaAtual, setPaginaAtual] = useState<number>(1);
  const [carregamento, setCarregamento] = useState(true);


  const buscarExpedicoes = () => {
    const quantidadeDeExpedicoesAtuais = expedicao?.expedicoes.length || 0;

    const token = Cookies.get("token");
    api
      .post(
        "/expedicao/lista",
        {
          pagina: paginaAtual,
          quantidade: quantidadeFiltro,
          status: status,
          filtro: filtro,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      )
      .then((response) => {
        setExpedições(response.data);

        const quantidadeNecessaria = quantidadeDeExpedicoesAtuais - response.data.expedicoes.length;


        if (quantidadeNecessaria < 0) {
          return
        }

        if (quantidadeNecessaria >= quantidadeDeExpedicoesAtuais) {
          return
        } else {
          const criarCardsTemporarios = Array(quantidadeNecessaria).fill(
            {
              id: 0,
              remetente: "",
              criadoEm: "",
              produtosEnviados: [],
              status: "falso",
            }
          )

          setExpedições({
            ...response.data,
            expedicoes: [...response.data.expedicoes, ...criarCardsTemporarios]
          })

          const quantidadeDePedidos = expedicao?.expedicoes.length || 0;

          let timeout = 1000

          switch (quantidadeDePedidos as number) {
            case 10:
              timeout = 500;
              break;

            case 20:
              timeout = 1000;
              break;

            case 50:
              timeout = 2000;
              break;
          }


          setTimeout(() => {
            setExpedições({
              ...response.data,
              expedicoes: [...response.data.expedicoes]

            })
          }, timeout);


        }
      })
      .catch((error) => {
        console.error("Erro ao buscar as expedições:", error);
      })
      .finally(() => {
        setCarregamento(false);
      });
  };

  // useEffect(() => {
  //   buscarExpedicoes();
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [paginaAtual]);

  useEffect(() => {
    buscarExpedicoes();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [status, quantidadeFiltro, filtro, paginaAtual]);

  const alterarStatus = (
    idDaExpedição: number,
    idDoProduto: number,
    novoStatus: string
  ) => {
    const token = Cookies.get("token");
    api
      .patch(
        `/expedicao/${idDaExpedição}`,
        {
          id: idDoProduto,
          status: novoStatus,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      )
      .then(() => {
        buscarExpedicoes();

        if (novoStatus === "Aprovado") {
          toast.dismiss();
          toast.success("Expedição aprovada com sucesso");
        } else {
          toast.dismiss();
          toast.success("Expedição recusada com sucesso");
        }
      })
      .catch(() => {
        toast.dismiss();
        toast.error("Erro ao alterar o status da expedição");
      });
  };

  const ultimaPagina = expedicao?.total
    ? Math.ceil(expedicao?.total / quantidadeFiltro)
    : 1;


  const modificarPagina = (pagina: number) => {

    setPaginaAtual(pagina);
    setTimeout(() => {
      document
        .querySelector("#conteudoContainer")
        ?.scrollTo({ top: 0, left: 0, behavior: "smooth" });
    }, 0);

  };

  const sincronizar = () => {
    buscarExpedicoes();
    toast.dismiss();
    toast.success("Sincronizado com sucesso");
  };

  return {
    expedicao,
    setExpedições,
    setStatus,
    setQuantidade,
    setFiltro,
    setPaginaAtual,
    setCarregamento,
    buscarExpedicoes,
    status,
    quantidadeFiltro,
    filtro,
    paginaAtual,
    carregamento,
    alterarStatus,
    modificarPagina,
    sincronizar,
    ultimaPagina,
  }
};

export default useAprovarExpedicao;