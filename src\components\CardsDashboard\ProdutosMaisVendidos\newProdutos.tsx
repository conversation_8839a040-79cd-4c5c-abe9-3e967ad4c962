import { PropsProdutosMaisVendidos } from "../../../interfaces";
import CardDash from "../card"


const NewProdutosMaisVendidos: React.FC<PropsProdutosMaisVendidos> = ({ dados }) => {

    const produtosVendidos: { [produto: string]: number } = {};

 
    if (dados) {
        dados.forEach(pedido => {
          pedido.itensPedido.forEach(item => {
            if (produtosVendidos[item.produto]) {
              produtosVendidos[item.produto] += item.quantidade;
            } else {
              produtosVendidos[item.produto] = item.quantidade;
            }
          });
        });
      }

      const produtosOrdenados = Object.keys(produtosVendidos).sort((a, b) => produtosVendidos[b] - produtosVendidos[a]).slice(0, 5);


      


    const naoAProdutos = () => {
        return (
            <>
            <CardDash titulo="Produtos mais vendidos">
              <div className="divCardDashboard divCardVazio">
                <ul className="ulCardVazio">
                  <li className="liCardVazio">
                    Não há dados para exibir  
                  </li>
                </ul>
              </div>
            </CardDash>
          </>
        )
    }
    
    if (!produtosOrdenados.length) {
      return naoAProdutos();
    }


    return (
        <>
        <CardDash titulo="Produtos mais vendidos">
        <div className="divCardDashboard">
            <ul className="ulCardDashboard">
            {produtosOrdenados.map((produto, index) => (
              <li key={index} className="liCardDashboard">
                {produto} 
                <span className="spanCardNumero">{produtosVendidos[produto]}</span> 
              </li>
             
            ))}
           
            </ul>
        </div>
        </CardDash>
        </>
    )
}
export default NewProdutosMaisVendidos