import Cookies from "js-cookie";
import { useEffect, useState } from "react";

import api from "../apiService";
import { toast } from "react-toastify";
import { DashboarI, Pedido } from "../interfaces";

const useDashboard = () => {
    const [dataInicial, setDataInicial] = useState<Date | null>();
    const [dataFinal, setDataFinal] = useState<Date | null>();
    const [dadosPedidos, setDadosPedidos] = useState<Pedido[] | null>(null);
    const [dadosGraficoLinha, setDadosGraficoLinha] = useState<any[]>([]);
    const [datasValidas, setDatasValidas] = useState(true);
    const [mostrarPorValor, setMostrarPorValor] = useState(true);
    const [mostrarModalRelaorio, setMostrarModalRelatorio] = useState(false);
    const [dadosRelatorio, setDadosrelatorio] = useState<any[]>([]);
    const [carregando, setCarregando] = useState(true);
    const [paginaAtualGrafico, setPaginaAtualGrafico] = useState(0);
    const [alturaMaximaGrafico, setAlturaMaximaGrafico] = useState(0);
    const [FormaPagamento, setFormaPagamento] = useState("");
    const [idVendedor, setIdVendedor] = useState(0);
    const [vendedor, setVendedor] = useState<any[]>([]);
    const [vendedorSelecionado, setVendedorSelecionado] = useState({ value: 0, label: "Todos" });
    const [formaPagamentoSelecionada, setFormaPagamentoSelecionada] = useState({ value: "", label: "Todas" });
    const [dadosPorPagina, setDadosPorPagina] = useState(4);
    const [botaoAdicionarPorPagina, setBotaoAdicionarPorPagina] = useState(false);
    const [entradaPortas, setEntradaPortas] = useState(0);
    const [saidaPortas, setSaidaPortas] = useState(0);

    const datas = () => {
        const hoje = new Date();
        const seteDiasAtras = new Date(hoje);
        seteDiasAtras.setDate(hoje.getDate() - 30);

        setDataInicial(seteDiasAtras);
        setDataFinal(hoje);
    };

    const dataInicialFormatada = dataInicial;
    const dataFinalFormatada = dataFinal;

    useEffect(() => {
        BuscarPedidosFiltrados(dataInicial as Date, dataFinal as Date, idVendedor, FormaPagamento);
    }, [dataInicial, dataFinal, idVendedor, FormaPagamento]);

    useEffect(() => {
        organizarInformacoesGraficos();
        //  eslint-disable-next-line react-hooks/exhaustive-deps
    }, [dataInicial, dataFinal, dadosPedidos, idVendedor, FormaPagamento]);

    const organizarInformacoesGraficos = () => {
        if (!dadosPedidos) {
            setDadosGraficoLinha([]);
            setAlturaMaximaGrafico(0);
            return;
        }

        if (dadosPedidos) {
            const dataMap = new Map();
            const nomesDias = ["Domingo", "Segunda", "Terça", "Quarta", "Quinta", "Sexta", "Sábado"];

            dadosPedidos.forEach(pedido => {
                const dateObj = new Date(pedido.criadoEm);
                const dateISO = dateObj.toISOString().substr(0, 10);
                const dataFormatada = `${dateObj.getDate()}/${dateObj.getMonth() + 1}/${dateObj.getFullYear()}`;
                const nomeDia = nomesDias[dateObj.getDay()];

                if (!dataMap.has(dateISO)) {
                    dataMap.set(dateISO, {
                        total: 0,
                        contagem: 0,
                        dataFormatada,
                        nomeDia,
                    });
                }

                dataMap.get(dateISO).total += parseFloat(pedido.total);
                dataMap.get(dateISO).contagem += 1;
            });

            const dadosGraficoLinhaFormatados = Array.from(dataMap.values())
                .filter(dataEntry => (mostrarPorValor ? dataEntry.total > 0 : dataEntry.contagem > 0))
                .map(dataEntry => ({
                    x: dataEntry.dataFormatada,
                    y: mostrarPorValor ? dataEntry.total : dataEntry.contagem,
                    label: `${dataEntry.contagem} vendas\nR$ ${dataEntry.total.toFixed(2)}`,
                    nomeDia: dataEntry.nomeDia,
                }));

            let maximo = 0;
            dadosGraficoLinhaFormatados.forEach(dataEntry => {
                if (dataEntry.y > maximo) {
                    maximo = dataEntry.y;
                }
            });

            setAlturaMaximaGrafico(maximo);

            setDadosGraficoLinha(dadosGraficoLinhaFormatados);
        }
    };

    useEffect(() => {
        datas();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // useEffect(() => {
    //   aplicarFiltro();
    //   // eslint-disable-next-line react-hooks/exhaustive-deps
    // }, [dataInicial, dataFinal, idVendedor, FormaPagamento]);

    useEffect(() => {
        // contagemPagamentosPedidos();
        organizarInformacoesGraficos();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [dadosPedidos, mostrarPorValor]);

    const manipularAbrirPDF = () => {
        setMostrarModalRelatorio(true);
    };

    const BuscarPedidosFiltrados = async (
        dataInicial: Date | null,
        dataFinal: Date | null,
        idVendedor?: number,
        FormaPagamento?: string
    ) => {
        setPaginaAtualGrafico(0);

        const dataInicialToISO = dataInicial?.toISOString().slice(0, 10);
        const dataFinalToISO = dataFinal?.toISOString().slice(0, 10);

        try {
            if (!dataInicialToISO || !dataFinalToISO) {
                return;
            }

            const token = Cookies.get("token");

            let urlPadrao = `/pedidos/filtro/${dataInicialToISO}/${dataFinalToISO}`;

            if (idVendedor) {
                urlPadrao = `/pedidos/filtro/${dataInicialToISO}/${dataFinalToISO}/${idVendedor}`;
            }

            if (FormaPagamento) {
                urlPadrao = `/pedidos/filtro/${dataInicialToISO}/${dataFinalToISO}/${idVendedor}/${FormaPagamento}`;
            }

            const resposta = await api.get<DashboarI>(urlPadrao, {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            });
            setCarregando(false);

            setEntradaPortas(resposta.data.entradaPortas);
            setSaidaPortas(resposta.data.saidaPortas);
            setDadosPedidos(resposta.data.pedidos);
            setDadosrelatorio(resposta.data.pedidos);

            return resposta.data;
        } catch (error) {
            console.error(error);

            throw new Error("Erro ao buscar pedidos");
        }
    };

    const aplicarFiltro = async () => {
        setPaginaAtualGrafico(0);

        setDatasValidas(true);

        try {
            if (dataFinal === undefined || dataInicial === undefined) {
                toast.dismiss();
                toast.error("Data final inválida");
                return;
            }

            toast.dismiss();
            toast.loading("Aplicando filtros...");

            await BuscarPedidosFiltrados(dataInicial, dataFinal, idVendedor, FormaPagamento);

            toast.dismiss();
            toast.success("Filtros aplicados");
        } catch (erro) {
            toast.dismiss();
            toast.error("Erro ao aplicar filtros");
            console.error(erro);
        } finally {
            setCarregando(false);
        }
    };

    const ordenarGraficoPorData = () => {
        const dadosGraficoLinhaOrdenados = dadosGraficoLinha.sort(function (a, b) {
            return new Date(a.x).getTime() - new Date(b.x).getTime();
        });
        return dadosGraficoLinhaOrdenados;
    };

    const dadosGraficoPaginados = () => {
        const dadosPorPaginaOrdenados = ordenarGraficoPorData();
        const inicio = paginaAtualGrafico * dadosPorPagina;
        const fim = inicio + dadosPorPagina;
        return dadosPorPaginaOrdenados.slice(inicio, fim);
    };

    useEffect(() => {
        dadosGraficoPaginados();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [dadosPorPagina]);

    const buscarVendedor = async () => {
        const token = Cookies.get("token");

        try {
            const resposta = await api.get(`/usuario/vendedores`, {
                headers: {
                    Authorization: token ? `Bearer ${token}` : undefined,
                    "Content-Type": "application/json",
                },
            });

            setVendedor(resposta.data);
        } catch (erro) {
            console.error(erro);
        }
    };

    useEffect(() => {
        buscarVendedor();
        BuscarPedidosFiltrados(dataInicial || null, dataFinal || null);
    }, []);

    const graficoPorPagina = () => {
        if (botaoAdicionarPorPagina === false) {
            setPaginaAtualGrafico(0);
            if (dadosGraficoLinha.length > 10) {
                setDadosPorPagina(10);
            } else {
                setDadosPorPagina(dadosGraficoLinha.length);
            }
            setBotaoAdicionarPorPagina(true);
        } else {
            setPaginaAtualGrafico(0);
            setDadosPorPagina(4);
            setBotaoAdicionarPorPagina(false);
        }
    };

    return {
        dataInicial,
        setDataInicial,
        dataFinal,
        setDataFinal,
        dadosPedidos,
        setDadosPedidos,
        entradaPortas,
        saidaPortas,
        dadosGraficoLinha,
        setDadosGraficoLinha,
        datasValidas,
        setDatasValidas,
        mostrarPorValor,
        setMostrarPorValor,
        mostrarModalRelaorio,
        setMostrarModalRelatorio,
        dadosRelatorio,
        setDadosrelatorio,
        carregando,
        setCarregando,
        paginaAtualGrafico,
        setPaginaAtualGrafico,
        alturaMaximaGrafico,
        setAlturaMaximaGrafico,
        FormaPagamento,
        setFormaPagamento,
        idVendedor,
        setIdVendedor,
        vendedor,
        setVendedor,
        dadosPorPagina,
        setDadosPorPagina,
        botaoAdicionarPorPagina,
        setBotaoAdicionarPorPagina,
        datas,
        dataInicialFormatada,
        dataFinalFormatada,
        // contagemPagamentosPedidos,
        organizarInformacoesGraficos,
        manipularAbrirPDF,
        aplicarFiltro,
        dadosGraficoPaginados,
        buscarVendedor,
        graficoPorPagina,
        vendedorSelecionado,
        setVendedorSelecionado,
        formaPagamentoSelecionada,
        setFormaPagamentoSelecionada,
    };
};

export default useDashboard;
