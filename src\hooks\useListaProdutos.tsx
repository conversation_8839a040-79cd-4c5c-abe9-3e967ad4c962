import Cookies from "js-cookie";
import { toast } from "react-toastify";
import api from "../apiService";
import { useEffect, useState } from "react";

import { AxiosError } from "axios";
import { ProdutoLista } from "../interfaces";




const useListaProdutos = () => {
    const [produtos, setProdutos] = useState<ProdutoLista[]>([]);
    const [carregando, setCarregando] = useState(true);
  
    useEffect(() => {
      const token = Cookies.get("token");
  
      buscarProdutos("/produtos", token)
        .then((dados) => setProdutos(dados))
        .catch((erro) => console.error(erro.message));
    }, []);
  
    const buscarProdutos = async (url: string, token: string | undefined) => {
      const resposta = await api.get(url, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      setCarregando(false);
      const dados = await resposta.data;
  
      return dados;
    };
  
  
    const excluirProduto = async (id: number) => {
      try {
        const token = Cookies.get("token");
        const resposta = await api.delete(`/produtos/${id}`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });
        if (resposta.status !== 200) {
          throw new Error(resposta.statusText);
        } 
  
        
        toast.dismiss();
        toast.success(resposta.data.message);
        
        setProdutos((prevProdutos) =>
          prevProdutos.filter((produto) => produto.id !== id)
        );
  
        
      } catch (error) {
        const erro = error as AxiosError;
  
        if (erro.response?.data) {
          toast.dismiss();
          toast.error((erro.response?.data as { message: string }).message)
        } else {
          toast.dismiss();
          toast.error("Erro ao excluir o produto");
        }
      }
    };

    return {
        produtos,
        setProdutos,
        carregando,
        setCarregando,
        buscarProdutos,
        excluirProduto
    }
};

export default useListaProdutos;