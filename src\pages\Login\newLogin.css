.Login {
    display: flex;
    background-color: black;
    width: 100%;
    height: 100vh;
}

.divLogin1 {
    display: flex;
    position: relative;
    width: 80%;
    background-color: rgba(43, 43, 43, 0.877);
    height: 100%;
    align-items: center;
    box-shadow: 2px 0px 10px 0px #1b1b1b;
}


.logoLogin {
    width: 50px;
    height: 50px;
}



.logoLoginDiv {
    position: absolute;
    width: 70%;
    height: 70%;
    display: flex;
    gap: 20px;
    border-radius: 30px;
    text-align: center;
    color: rgba(146, 141, 141, 0.795);
    flex-direction: column;
    font-size: 15px;
    justify-content: center;
    align-items: center;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.logoLoginDiv img {
    width: 50%;
}




.versaoDiv {
    position: absolute;
    font-size: 12px;
    right: 0;
    top: 0;
    margin: 10px;
    text-shadow: 1px 1px 1px black;
    color: #5e5c5c;
    font-family: var(--font-main);
}

.divLogin2 {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 50%;
    height: 100%;
}


.loginDivCampos {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 80%;
    height: 70%;
    border-radius: 15px;
}

.formLogin {
    display: flex;
    color: #969595;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.divCamposForm {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3px;
    border-radius: 10px;
    width: 90%;
}

.inputLogin {
    width: 100%;
    height: 50px;

    background-color: #000000d8;

    border: none;
    border-bottom: 1px solid #009f3d6c;
    padding: 4px;
    color: white;
    font-size: 20px;

    margin-top: 8px;
    outline: none;
    /* margin: 8px; */
    font-family: var(--font-main);
}

.inputLogin:focus {
    border-bottom: 1px solid #009f3d6c;
}

.inputLogin:-webkit-autofill {
    background-color: transparent !important;
    caret-color: inherit !important;
    color: white !important;
    -webkit-text-fill-color: white !important;
    transition: background-color 5000s ease-in-out 0s;
}


.inputLogin:-webkit-autofill:focus {
    background-color: transparent !important;

}


.botaoLogin {
    width: 100%;
    height: 50px;
    border-radius: 10px;
    font-size: 18px;
    border: none;
    background-color: #009f3d6c;
    color: var(--second-color);
    font-weight: bold;
    font-family: var(--font-main);
    margin-top: 45px;
}

.botaoLogin:hover {
    background-color: #036629;
    color: white;
}

.logoD {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 90%;
    border-radius: 5px;
    height: 90%;
    margin-bottom: 30px;
    /* background-color: #0c0c0c; */
    /* box-shadow: -1px -1px 6px 1px rgb(255 255 255 / 21%); */
    box-shadow: 1px;
    flex-direction: column;
}

.h1Login {
    color: var(--second-color);
    justify-self: self-start;
    align-self: self-start;
    font-family: var(--font-main);
    margin-left: 30px;
    font-size: 30px;
    margin-bottom: 25px;
}

.MostrarSenha {
    width: 100%;
    height: auto;
    display: flex;
    position: relative;
    margin: 0 auto;
}

.botaoMostrarSenha {
    position: absolute;
    right: 3px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    color: var(--second-color);
    font-size: 20px;
}

.iconeSenhaLogin {
    position: absolute;
    top: 45%;
    right: 10px;
    transform: translateY(-50%);
    color: gray;
    width: 20px;
    height: 20px;
}

.divManterConectadoNovo {
    display: flex;
    width: 100%;
    flex-direction: row;
    margin-top: 25px;
    align-items: center;
    justify-content: center;
}

.inputManterConectado {
    margin-right: 6px;
}

.labelManterConectado {
    font-size: 18px;
    font-family: var(--font-main);
}

input:checked {
    accent-color: var(--primary-color);

}

.pLogin {
    color: rgba(146, 141, 141, 0.795);
    font-family: var(--font-main);
    width: 90%;
}


.divLogo2 {
    display: flex;
    display: none;
    /* background-color: rgba(43, 43, 43, 0.877); */
    box-shadow: 1px 1px 6px 1px rgb(255 255 255 / 21%);
    width: 100%;
    height: 6%;
    justify-content: flex-start;
    align-items: center;

    position: absolute;
}

.divLogo2 img {
    width: 17%;
    /* top: 0;
    right: 0;
    margin: 30px; */
    position: absolute;
    margin-left: 20px;

}

.manterConectadoCheck {
    width: 20px;
    margin-left: 3px;
}


@media screen and (max-width:950px) {
    .divLogin1 {
        width: 50%;
    }

    .logoLoginDiv img {
        width: 80%;
    }

    .pLogin {
        font-size: 11px;
    }

    .manterConectadoCheck {
        width: 20px;
        margin-top: 3px;
        margin-left: 2px;
    }
}

@media screen and (max-width:680px) {

    .divManterConectadoNovo {
        flex-direction: row;
        display: flex;
        align-items: center;
        height: 20px;
        justify-content: center;
    }

    #manterConectadoCheck {
        width: 20px;
        margin-top: 5px;
    }
    .divLogo2 {
        display: flex;
    }

    .divLogin1 {
        display: none;
    }

    .divLogin2 {
        width: 100%;
    }

    .divCamposForm {
        width: 100%;
    }

    .logoD {
        width: 100%;
    }

    .loginDivCampos {
        width: 87%;
    }

    .h1Login {
        margin-left: 7px;
    }
}