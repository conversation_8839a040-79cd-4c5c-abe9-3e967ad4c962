import { AiOutlineEye, AiOutlineEyeInvisible } from "react-icons/ai";
import useLogin from "../../hooks/useLogin";
import "./newLogin.css"
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import packageJson from '../../../package.json';

const NewLogin = () => {
    const { mostrarSenha, aoSubmeter, alternarSenha, setLogin, setSenha, setSign } = useLogin();

    return (
        <>
            <div className="Login">
                <div className="divLogin1">
                    <div className="versaoDiv">
                        <p>Versão: {packageJson.version}</p>
                    </div>
                    <div className="logoLoginDiv">

                        <img src="/FRP_BRANCO.png" alt="logoSistema" />
                        <p className="pLogin">FRP Sistema interno de gestão de estoque e vendas do Feirão Pormade. Otimizando processos e impulsionando resultados.</p>

                    </div>

                </div>


                <div className="divLogo2">
                    <img src="/FRP_2_BRANCO.png" alt="logoSistema" />
                </div>
                <div className="divLogin2">
                    <div className="loginDivCampos">
                    <form className="formLogin" autoComplete="off" onSubmit={aoSubmeter}>
                            <div className="logoD">
                                <h1 className="h1Login">LOGIN</h1>

                                <div className="divCamposForm">
                                    <input
                                        className="inputLogin"
                                        type="text"
                                        placeholder="Usuário"
                                        autoComplete="off"
                                        onChange={(e) => setLogin(e.target.value)}

                                    />
    <div className="MostrarSenha">
                                <input
                                    className="inputLogin"
                                    type={mostrarSenha ? "text" : "password"}
                                    placeholder="Senha"
                                    autoComplete="off"
                                    onChange={(e) => setSenha(e.target.value)}
                                />
                                <button type="button" className="botaoMostrarSenha" onClick={alternarSenha}>
                                    {mostrarSenha ? <AiOutlineEyeInvisible className="iconeSenhaLogin" /> : <AiOutlineEye className="iconeSenhaLogin" />}
                                </button>
                            </div>
                            <button type="submit" className="botaoLogin" >
                                Entrar
                            </button>

                                    <div className="divManterConectadoNovo">
                                        <input
                                            type="checkbox"
                                            id="manterConectado"
                                            className="manterConectadoCheck"
                                            onChange={(e) => setSign(e.target.checked)}
                                        />
                                        <label className="labelManterConectado" htmlFor="manterConectado">
                                            Manter conectado?
                                        </label>
                                    </div>
                                </div>
                            </div>

                        </form>
                    </div>
                </div>
                <ToastContainer
                    position="top-right"
                    autoClose={5000}
                    hideProgressBar={false}
                    newestOnTop={false}
                    closeOnClick
                    rtl={false}
                    pauseOnFocusLoss
                    draggable
                    pauseOnHover
                    theme="dark"
                />
            </div>
        </>
    )
}

export default NewLogin;